import {
  createRedteamConfig,
  startRedteamJob,
  getRedteamResults,
  getRedteamPlugins,
  getRedteamStrategies,
  cancelRedteamJob,
  getRedteamReport
} from "../promptfooRedteamService";

describe("PromptFoo Red Team Service E2E Tests", ()=>{
  // This prevents tests from timing out too quickly
  jest.setTimeout(30000);

  let testConfigId: string;
  let testJobId: string;

  beforeAll(()=>{
    // Ensure we have an API token for tests
    if(!process.env.PROMPTFOO_API_TOKEN) {
      throw new Error("PROMPTFOO_API_TOKEN environment variable must be set to run E2E tests");
    }
  });

  describe("Full red team evaluation flow", ()=>{
    it("should create config, run job, and get results", async()=>{
      // 1. Create a red team config
      const config = {
        purpose: "E2E test configuration for a simple chat assistant",
        plugins: [
          { id: "harmful:hate", numTests: 2 },
          { id: "prompt-injection", numTests: 2 }
        ],
        strategies: ["jailbreak"],
        language: "English"
      };

      const configResult = await createRedteamConfig(config);
      expect(configResult.id).toBeDefined();
      testConfigId = configResult.id;

      // 2. Start a red team job
      const jobConfig = {
        target: {
          id: "openai:gpt-3.5-turbo",
          config: {
            temperature: 0.7,
            maxTokens: 150
          }
        },
        prompts: [
          "You are a helpful assistant. User: {{prompt}}"
        ],
        configId: testConfigId
      };

      const jobResult = await startRedteamJob(jobConfig);
      expect(jobResult.jobId).toBeDefined();
      testJobId = jobResult.jobId;

      // 3. Poll for results
      let results;
      let attempts = 0;
      const maxAttempts = 10;

      while(attempts < maxAttempts) {
        results = await getRedteamResults({ jobId: testJobId });
        if(results.status === "completed") {
          break;
        }
        await new Promise(resolve=>setTimeout(resolve, 3000)); // Wait 3 seconds between polls
        attempts++;
      }

      expect(results.status).toBe("completed");
      expect(results.results).toBeDefined();

      // 4. Get detailed report
      const report = await getRedteamReport(testJobId);
      expect(report.summary).toBeDefined();
      expect(report.vulnerabilities).toBeDefined();
    });
  });

  describe("Plugin and strategy discovery", ()=>{
    it("should fetch available plugins", async()=>{
      const plugins = await getRedteamPlugins();
      expect(Array.isArray(plugins)).toBe(true);
      expect(plugins.length).toBeGreaterThan(0);
    });

    it("should fetch plugins by category", async()=>{
      const securityPlugins = await getRedteamPlugins("security");
      expect(Array.isArray(securityPlugins)).toBe(true);
      securityPlugins.forEach(plugin=>{
        expect(plugin.category).toBe("security");
      });
    });

    it("should fetch available strategies", async()=>{
      const strategies = await getRedteamStrategies();
      expect(Array.isArray(strategies)).toBe(true);
      expect(strategies.length).toBeGreaterThan(0);
    });
  });

  describe("Job control", ()=>{
    it("should be able to cancel a running job", async()=>{
      // Start a new job
      const config = {
        purpose: "Test job for cancellation",
        plugins: [{ id: "harmful:hate", numTests: 5 }]
      };
      const configResult = await createRedteamConfig(config);

      const jobConfig = {
        target: {
          id: "openai:gpt-3.5-turbo"
        },
        configId: configResult.id
      };
      const jobResult = await startRedteamJob(jobConfig);

      // Cancel it
      const cancelResult = await cancelRedteamJob(jobResult.jobId);
      expect(cancelResult.status).toBe("cancelled");

      // Verify it"s cancelled
      const results = await getRedteamResults({ jobId: jobResult.jobId });
      expect(results.status).toBe("cancelled");
    });
  });

  // Clean up test resources if needed
  afterAll(async()=>{
    if(testJobId) {
      try {
        await cancelRedteamJob(testJobId);
      } catch (error) {
        console.log("Clean up error:", error);
      }
    }
  });
});
