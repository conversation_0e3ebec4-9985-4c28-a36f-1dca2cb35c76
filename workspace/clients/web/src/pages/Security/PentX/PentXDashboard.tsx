import React, { useState, useEffect } from 'react';
import { useAuth0Fetch } from '../../../util/fetch';
import './PentXDashboard.module.css';

interface PentXTest {
  id: string;
  name: string;
  targetType: 'web-app' | 'api' | 'infrastructure' | 'network';
  status: 'queued' | 'running' | 'completed' | 'failed' | 'cancelled';
  createdAt: string;
  completedAt?: string;
  vulnerabilitiesFound: number;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
}

interface PentXConfig {
  apiKey: string;
  baseUrl: string;
  defaultTimeout: number;
  maxConcurrentTests: number;
}

const PentXDashboard: React.FC = () => {
  const [tests, setTests] = useState<PentXTest[]>([]);
  const [config, setConfig] = useState<PentXConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showNewTestModal, setShowNewTestModal] = useState(false);
  const fetch = useAuth0Fetch();

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // Load PentX tests
      const testsResponse = await fetch('/api/security/pentx/tests');
      if (testsResponse.ok) {
        const testsData = await testsResponse.json();
        setTests(testsData.tests || []);
      }

      // Load PentX configuration
      const configResponse = await fetch('/api/security/pentx/config');
      if (configResponse.ok) {
        const configData = await configResponse.json();
        setConfig(configData.config);
      }

    } catch (err) {
      setError(`Failed to load dashboard data: ${(err as Error).message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTest = async (testConfig: any) => {
    try {
      const response = await fetch('/api/security/pentx/tests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testConfig),
      });

      if (response.ok) {
        const newTest = await response.json();
        setTests(prev => [newTest, ...prev]);
        setShowNewTestModal(false);
      } else {
        throw new Error('Failed to create test');
      }
    } catch (err) {
      setError(`Failed to create test: ${(err as Error).message}`);
    }
  };

  const handleCancelTest = async (testId: string) => {
    try {
      const response = await fetch(`/api/security/pentx/tests/${testId}/cancel`, {
        method: 'POST',
      });

      if (response.ok) {
        setTests(prev => prev.map(test => 
          test.id === testId ? { ...test, status: 'cancelled' } : test
        ));
      }
    } catch (err) {
      setError(`Failed to cancel test: ${(err as Error).message}`);
    }
  };

  const getStatusColor = (status: PentXTest['status']) => {
    switch (status) {
      case 'queued': return '#fbbf24';
      case 'running': return '#3b82f6';
      case 'completed': return '#10b981';
      case 'failed': return '#ef4444';
      case 'cancelled': return '#6b7280';
      default: return '#6b7280';
    }
  };

  const getRiskColor = (riskLevel: PentXTest['riskLevel']) => {
    switch (riskLevel) {
      case 'low': return '#10b981';
      case 'medium': return '#fbbf24';
      case 'high': return '#f97316';
      case 'critical': return '#ef4444';
      default: return '#6b7280';
    }
  };

  if (loading) {
    return (
      <div className="pentx-dashboard loading">
        <div className="loading-spinner">Loading PentX Dashboard...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="pentx-dashboard error">
        <div className="error-message">
          <h3>Error Loading Dashboard</h3>
          <p>{error}</p>
          <button onClick={loadDashboardData}>Retry</button>
        </div>
      </div>
    );
  }

  return (
    <div className="pentx-dashboard">
      <header className="dashboard-header">
        <h1>🤖 PentX Autonomous Penetration Testing</h1>
        <div className="header-actions">
          <button 
            className="btn-primary"
            onClick={() => setShowNewTestModal(true)}
            disabled={!config?.apiKey}
          >
            New Test
          </button>
          <button 
            className="btn-secondary"
            onClick={loadDashboardData}
          >
            Refresh
          </button>
        </div>
      </header>

      {!config?.apiKey && (
        <div className="config-warning">
          <h3>⚠️ PentX Configuration Required</h3>
          <p>Please configure your PentX API key to start autonomous penetration testing.</p>
          <button className="btn-primary">Configure PentX</button>
        </div>
      )}

      <div className="dashboard-stats">
        <div className="stat-card">
          <h3>Total Tests</h3>
          <div className="stat-value">{tests.length}</div>
        </div>
        <div className="stat-card">
          <h3>Running Tests</h3>
          <div className="stat-value">
            {tests.filter(t => t.status === 'running').length}
          </div>
        </div>
        <div className="stat-card">
          <h3>Completed Tests</h3>
          <div className="stat-value">
            {tests.filter(t => t.status === 'completed').length}
          </div>
        </div>
        <div className="stat-card">
          <h3>Critical Issues</h3>
          <div className="stat-value critical">
            {tests.filter(t => t.riskLevel === 'critical').length}
          </div>
        </div>
      </div>

      <div className="tests-section">
        <h2>Recent Tests</h2>
        {tests.length === 0 ? (
          <div className="empty-state">
            <h3>No tests found</h3>
            <p>Create your first autonomous penetration test to get started.</p>
            <button 
              className="btn-primary"
              onClick={() => setShowNewTestModal(true)}
              disabled={!config?.apiKey}
            >
              Create First Test
            </button>
          </div>
        ) : (
          <div className="tests-grid">
            {tests.map(test => (
              <div key={test.id} className="test-card">
                <div className="test-header">
                  <h3>{test.name}</h3>
                  <div 
                    className="status-badge"
                    style={{ backgroundColor: getStatusColor(test.status) }}
                  >
                    {test.status.toUpperCase()}
                  </div>
                </div>
                
                <div className="test-details">
                  <div className="detail-row">
                    <span>Target Type:</span>
                    <span className="target-type">{test.targetType}</span>
                  </div>
                  <div className="detail-row">
                    <span>Created:</span>
                    <span>{new Date(test.createdAt).toLocaleDateString()}</span>
                  </div>
                  {test.completedAt && (
                    <div className="detail-row">
                      <span>Completed:</span>
                      <span>{new Date(test.completedAt).toLocaleDateString()}</span>
                    </div>
                  )}
                  <div className="detail-row">
                    <span>Vulnerabilities:</span>
                    <span className="vuln-count">{test.vulnerabilitiesFound}</span>
                  </div>
                  <div className="detail-row">
                    <span>Risk Level:</span>
                    <span 
                      className="risk-badge"
                      style={{ color: getRiskColor(test.riskLevel) }}
                    >
                      {test.riskLevel.toUpperCase()}
                    </span>
                  </div>
                </div>

                <div className="test-actions">
                  <button className="btn-secondary">View Report</button>
                  {test.status === 'running' && (
                    <button 
                      className="btn-danger"
                      onClick={() => handleCancelTest(test.id)}
                    >
                      Cancel
                    </button>
                  )}
                  {test.status === 'completed' && (
                    <button className="btn-primary">Download Report</button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {showNewTestModal && (
        <NewTestModal
          onClose={() => setShowNewTestModal(false)}
          onSubmit={handleCreateTest}
        />
      )}
    </div>
  );
};

interface NewTestModalProps {
  onClose: () => void;
  onSubmit: (config: any) => void;
}

const NewTestModal: React.FC<NewTestModalProps> = ({ onClose, onSubmit }) => {
  const [testName, setTestName] = useState('');
  const [targetType, setTargetType] = useState<'web-app' | 'api' | 'infrastructure'>('web-app');
  const [targetUrl, setTargetUrl] = useState('');
  const [depth, setDepth] = useState<'shallow' | 'medium' | 'deep'>('medium');
  const [timeLimit, setTimeLimit] = useState(60);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const config = {
      name: testName,
      targetType,
      target: {
        type: targetType,
        url: targetUrl,
      },
      scope: {
        depth,
        timeLimit,
      },
      options: {
        aggressive: false,
        stealthMode: true,
        socialEngineering: false,
        physicalTesting: false,
        complianceChecks: true,
      },
    };

    onSubmit(config);
  };

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h2>Create New PentX Test</h2>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>
        
        <form onSubmit={handleSubmit} className="test-form">
          <div className="form-group">
            <label>Test Name</label>
            <input
              type="text"
              value={testName}
              onChange={(e) => setTestName(e.target.value)}
              placeholder="Enter test name"
              required
            />
          </div>

          <div className="form-group">
            <label>Target Type</label>
            <select
              value={targetType}
              onChange={(e) => setTargetType(e.target.value as any)}
            >
              <option value="web-app">Web Application</option>
              <option value="api">API</option>
              <option value="infrastructure">Infrastructure</option>
            </select>
          </div>

          <div className="form-group">
            <label>Target URL</label>
            <input
              type="url"
              value={targetUrl}
              onChange={(e) => setTargetUrl(e.target.value)}
              placeholder="https://example.com"
              required
            />
          </div>

          <div className="form-group">
            <label>Test Depth</label>
            <select
              value={depth}
              onChange={(e) => setDepth(e.target.value as any)}
            >
              <option value="shallow">Shallow (Quick scan)</option>
              <option value="medium">Medium (Balanced)</option>
              <option value="deep">Deep (Comprehensive)</option>
            </select>
          </div>

          <div className="form-group">
            <label>Time Limit (minutes)</label>
            <input
              type="number"
              value={timeLimit}
              onChange={(e) => setTimeLimit(parseInt(e.target.value))}
              min="15"
              max="480"
            />
          </div>

          <div className="form-actions">
            <button type="button" className="btn-secondary" onClick={onClose}>
              Cancel
            </button>
            <button type="submit" className="btn-primary">
              Start Test
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default PentXDashboard;
