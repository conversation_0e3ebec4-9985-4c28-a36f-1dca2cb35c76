// PentX.ai Integration Usage Examples

import { RedTeamEngine } from '../../../core/engine';
import { ConfigTemplates } from '../../../core/config';
import { PluginManager } from '../../../plugins/manager';
import { PentXPluginConfig } from '../types';

/**
 * Example: Basic PentX Web Application Testing
 */
export async function basicWebAppTesting() {
  console.log('🚀 Starting PentX Web Application Testing Example');

  // Initialize the red team engine
  const engine = new RedTeamEngine();
  const pluginManager = new PluginManager();

  // Configure PentX plugin
  const pentxConfig: PentXPluginConfig = {
    apiKey: process.env.PENTX_API_KEY || 'your-pentx-api-key',
    baseUrl: 'https://api.pentx.ai/v1',
    defaultTimeout: 1800000, // 30 minutes
    maxConcurrentTests: 2,
    defaultScope: {
      depth: 'medium',
      timeLimit: 60,
    },
    defaultOptions: {
      aggressive: false,
      stealthMode: true,
      socialEngineering: false,
      physicalTesting: false,
      complianceChecks: true,
    },
    enableRealTimeUpdates: true,
  };

  // Register PentX plugin
  pluginManager.registerPentXPlugin(pentxConfig);

  // Create web app testing configuration
  const config = ConfigTemplates.pentxWebApp({
    url: 'https://example.com',
    purpose: 'Security assessment of example web application',
    depth: 'deep',
    timeLimit: 90,
    aggressive: false,
  });

  try {
    // Run the assessment
    console.log('🔍 Running PentX autonomous penetration test...');
    const report = await engine.runAssessment(config);

    // Display results
    console.log('✅ Assessment completed!');
    console.log(`📊 Summary: ${report.summary.vulnerabilitiesFound} vulnerabilities found`);
    console.log(`🎯 Risk Level: ${report.summary.riskLevel.toUpperCase()}`);
    console.log(`⏱️ Duration: ${Math.round(report.metadata.duration / 1000 / 60)} minutes`);

    // Export enhanced report with PentX details
    const reportGenerator = engine.reportGenerator;
    const enhancedReport = await reportGenerator.exportPentXReport(report, 'markdown');
    
    console.log('📄 Enhanced report generated with PentX details');
    return enhancedReport;

  } catch (error) {
    console.error('❌ Assessment failed:', (error as Error).message);
    throw error;
  }
}

/**
 * Example: API Security Assessment
 */
export async function apiSecurityTesting() {
  console.log('🚀 Starting PentX API Security Testing Example');

  const engine = new RedTeamEngine();
  const pluginManager = new PluginManager();

  // Configure PentX for API testing
  const pentxConfig: PentXPluginConfig = {
    apiKey: process.env.PENTX_API_KEY || 'your-pentx-api-key',
    defaultScope: {
      depth: 'deep',
      timeLimit: 45,
    },
    defaultOptions: {
      aggressive: false,
      stealthMode: true,
      socialEngineering: false,
      physicalTesting: false,
      complianceChecks: true,
    },
  };

  pluginManager.registerPentXPlugin(pentxConfig);

  // Create API testing configuration
  const config = ConfigTemplates.pentxAPI({
    baseUrl: 'https://api.example.com',
    endpoints: ['/v1/users', '/v1/auth', '/v1/data', '/v1/admin'],
    purpose: 'API security assessment with PentX autonomous testing',
    depth: 'deep',
    timeLimit: 60,
    credentials: {
      bearerToken: 'test-token-for-authenticated-endpoints',
    },
  });

  try {
    const report = await engine.runAssessment(config);
    
    console.log('✅ API Assessment completed!');
    console.log(`📊 API Vulnerabilities: ${report.summary.vulnerabilitiesFound}`);
    
    // Filter API-specific vulnerabilities
    const apiVulns = report.vulnerabilities.filter(v => 
      v.type.includes('PENTX_API') || v.type.includes('API')
    );
    
    console.log(`🔌 API-specific issues: ${apiVulns.length}`);
    
    return report;

  } catch (error) {
    console.error('❌ API Assessment failed:', (error as Error).message);
    throw error;
  }
}

/**
 * Example: Comprehensive Multi-Target Testing
 */
export async function comprehensiveTesting() {
  console.log('🚀 Starting PentX Comprehensive Testing Example');

  const engine = new RedTeamEngine();
  const pluginManager = new PluginManager();

  const pentxConfig: PentXPluginConfig = {
    apiKey: process.env.PENTX_API_KEY || 'your-pentx-api-key',
    maxConcurrentTests: 3,
    defaultScope: {
      depth: 'deep',
      timeLimit: 120,
    },
    defaultOptions: {
      aggressive: true, // More aggressive for comprehensive testing
      stealthMode: false,
      socialEngineering: true,
      physicalTesting: false,
      complianceChecks: true,
    },
  };

  pluginManager.registerPentXPlugin(pentxConfig);

  // Create comprehensive testing configuration
  const config = ConfigTemplates.pentxComprehensive({
    url: 'https://example.com',
    ipRanges: ['***********/24'],
    purpose: 'Comprehensive security assessment with PentX autonomous testing',
    timeLimit: 180, // 3 hours
    aggressive: true,
  });

  try {
    console.log('🔍 Running comprehensive PentX assessment (this may take several hours)...');
    const report = await engine.runAssessment(config);

    console.log('✅ Comprehensive Assessment completed!');
    console.log(`📊 Total Vulnerabilities: ${report.summary.vulnerabilitiesFound}`);
    console.log(`🎯 Risk Level: ${report.summary.riskLevel.toUpperCase()}`);
    
    // Categorize vulnerabilities by type
    const vulnsByType = report.vulnerabilities.reduce((acc, vuln) => {
      const category = vuln.type.includes('WEB') ? 'Web' :
                     vuln.type.includes('API') ? 'API' :
                     vuln.type.includes('INFRASTRUCTURE') ? 'Infrastructure' :
                     vuln.type.includes('NETWORK') ? 'Network' : 'Other';
      acc[category] = (acc[category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    console.log('📈 Vulnerabilities by category:');
    Object.entries(vulnsByType).forEach(([category, count]) => {
      console.log(`  ${category}: ${count}`);
    });

    return report;

  } catch (error) {
    console.error('❌ Comprehensive Assessment failed:', (error as Error).message);
    throw error;
  }
}

/**
 * Example: Scheduled Automated Testing
 */
export async function scheduledTesting() {
  console.log('🚀 Setting up PentX Scheduled Testing Example');

  // This would typically be integrated with a job scheduler like cron
  const testSchedule = {
    webAppTesting: '0 2 * * 1', // Every Monday at 2 AM
    apiTesting: '0 3 * * 3',    // Every Wednesday at 3 AM
    comprehensiveTesting: '0 1 * * 6', // Every Saturday at 1 AM
  };

  console.log('📅 Scheduled testing configuration:');
  console.log('  - Web App Testing: Weekly on Mondays at 2 AM');
  console.log('  - API Testing: Weekly on Wednesdays at 3 AM');
  console.log('  - Comprehensive Testing: Weekly on Saturdays at 1 AM');

  // Example of how you might set up automated testing
  const automatedTests = [
    {
      name: 'Weekly Web App Security Scan',
      schedule: testSchedule.webAppTesting,
      testFunction: basicWebAppTesting,
    },
    {
      name: 'Weekly API Security Assessment',
      schedule: testSchedule.apiTesting,
      testFunction: apiSecurityTesting,
    },
    {
      name: 'Weekly Comprehensive Security Review',
      schedule: testSchedule.comprehensiveTesting,
      testFunction: comprehensiveTesting,
    },
  ];

  return automatedTests;
}

/**
 * Example: Error Handling and Monitoring
 */
export async function monitoredTesting() {
  console.log('🚀 Starting PentX Monitored Testing Example');

  const engine = new RedTeamEngine();
  
  // Add event handlers for monitoring
  engine.addEventHandler((event) => {
    switch (event.type) {
      case 'test_started':
        console.log('🟡 Test started:', event.data.config.purpose);
        break;
      case 'vulnerability_found':
        console.log('🔴 Vulnerability found:', event.data.vulnerabilities.length, 'issues');
        break;
      case 'test_completed':
        console.log('🟢 Test completed successfully');
        break;
      case 'error':
        console.error('❌ Test error:', event.data.error);
        break;
    }
  });

  const pluginManager = new PluginManager();
  const pentxConfig: PentXPluginConfig = {
    apiKey: process.env.PENTX_API_KEY || 'your-pentx-api-key',
    enableRealTimeUpdates: true,
  };

  pluginManager.registerPentXPlugin(pentxConfig);

  const config = ConfigTemplates.pentxWebApp({
    url: 'https://example.com',
    purpose: 'Monitored PentX security assessment',
  });

  try {
    const report = await engine.runAssessment(config);
    console.log('✅ Monitored assessment completed successfully');
    return report;
  } catch (error) {
    console.error('❌ Monitored assessment failed:', (error as Error).message);
    // Here you could send alerts, log to monitoring systems, etc.
    throw error;
  }
}

// Export all examples
export const PentXExamples = {
  basicWebAppTesting,
  apiSecurityTesting,
  comprehensiveTesting,
  scheduledTesting,
  monitoredTesting,
};
