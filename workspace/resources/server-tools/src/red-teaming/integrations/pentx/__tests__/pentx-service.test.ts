import { describe, it, expect, beforeEach, jest, afterEach } from '@jest/globals';
import { PentXService } from '../pentx-service';
import { PentXConfig, PentXTestRequest, PentXTestResponse, PentXResults } from '../types';

// Mock fetch globally
const mockFetch = jest.fn() as jest.MockedFunction<typeof fetch>;
global.fetch = mockFetch;

describe('PentXService', () => {
  let service: PentXService;
  let config: PentXConfig;

  beforeEach(() => {
    config = {
      apiKey: 'test-api-key',
      baseUrl: 'https://api.pentx.ai/v1',
      timeout: 30000,
      retryAttempts: 3,
    };

    service = new PentXService(config);
    mockFetch.mockClear();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Constructor', () => {
    it('should initialize with correct configuration', () => {
      expect(service.getConfig()).toEqual(config);
    });

    it('should use default base URL if not provided', () => {
      const serviceWithDefaults = new PentXService({ apiKey: 'test-key' });
      expect(serviceWithDefaults.getConfig().baseUrl).toBe('https://api.pentx.ai/v1');
    });
  });

  describe('healthCheck', () => {
    it('should return health status on success', async () => {
      const mockHealthData = {
        status: 'healthy' as const,
        version: '1.0.0',
        uptime: 12345,
        responseTime: 100,
        features: {
          webAppTesting: true,
          apiTesting: true,
          infrastructureTesting: true,
          networkTesting: true,
        },
        lastCheck: new Date(),
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: mockHealthData,
          requestId: 'req-123',
          timestamp: new Date(),
        }),
      } as Response);

      const result = await service.healthCheck();

      expect(result).toEqual(mockHealthData);
      expect(mockFetch).toHaveBeenCalledWith(
        'https://api.pentx.ai/v1/health',
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            'Authorization': 'Bearer test-api-key',
            'Content-Type': 'application/json',
          }),
        })
      );
    });

    it('should throw error on API failure', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        json: async () => ({
          success: false,
          error: {
            code: '500',
            message: 'Internal server error',
            timestamp: new Date(),
          },
        }),
      } as Response);

      await expect(service.healthCheck()).rejects.toThrow('PentX health check failed');
    });
  });

  describe('createTest', () => {
    it('should create test successfully', async () => {
      const testRequest: PentXTestRequest = {
        name: 'Test Web App',
        description: 'Testing web application security',
        config: {
          target: {
            type: 'web-app',
            url: 'https://example.com',
          },
          options: {
            aggressive: false,
            stealthMode: true,
            socialEngineering: false,
            physicalTesting: false,
            complianceChecks: true,
          },
        },
        priority: 'medium',
      };

      const mockResponse: PentXTestResponse = {
        testId: 'test-123',
        status: 'queued',
        estimatedDuration: 1800,
        queuePosition: 1,
        createdAt: new Date(),
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: mockResponse,
          requestId: 'req-123',
          timestamp: new Date(),
        }),
      } as Response);

      const result = await service.createTest(testRequest);

      expect(result).toEqual(mockResponse);
      expect(mockFetch).toHaveBeenCalledWith(
        'https://api.pentx.ai/v1/tests',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(testRequest),
        })
      );
    });

    it('should validate test request before sending', async () => {
      const invalidRequest: PentXTestRequest = {
        name: '', // Invalid: empty name
        config: {
          target: {
            type: 'web-app',
            // Missing required URL
          },
          options: {
            aggressive: false,
            stealthMode: true,
            socialEngineering: false,
            physicalTesting: false,
            complianceChecks: true,
          },
        },
      };

      await expect(service.createTest(invalidRequest)).rejects.toThrow('Test name is required');
    });

    it('should validate URL format for web-app targets', async () => {
      const invalidRequest: PentXTestRequest = {
        name: 'Test',
        config: {
          target: {
            type: 'web-app',
            url: 'invalid-url',
          },
          options: {
            aggressive: false,
            stealthMode: true,
            socialEngineering: false,
            physicalTesting: false,
            complianceChecks: true,
          },
        },
      };

      await expect(service.createTest(invalidRequest)).rejects.toThrow('Invalid URL format');
    });
  });

  describe('getTestStatus', () => {
    it('should return test status', async () => {
      const mockStatus = {
        testId: 'test-123',
        status: 'running' as const,
        progress: {
          percentage: 45,
          currentPhase: 'vulnerability-scanning',
          phasesCompleted: ['reconnaissance'],
          phasesRemaining: ['exploitation', 'reporting'],
        },
        startTime: new Date(),
        estimatedTimeRemaining: 900,
        vulnerabilitiesFound: 2,
        lastUpdate: new Date(),
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: mockStatus,
          requestId: 'req-123',
          timestamp: new Date(),
        }),
      } as Response);

      const result = await service.getTestStatus('test-123');

      expect(result).toEqual(mockStatus);
      expect(mockFetch).toHaveBeenCalledWith(
        'https://api.pentx.ai/v1/tests/test-123/status',
        expect.objectContaining({ method: 'GET' })
      );
    });
  });

  describe('getTestResults', () => {
    it('should return test results', async () => {
      const mockResults: PentXResults = {
        testId: 'test-123',
        status: 'completed',
        startTime: new Date(),
        endTime: new Date(),
        duration: 1800000,
        summary: {
          vulnerabilitiesFound: 3,
          criticalIssues: 1,
          highRiskIssues: 1,
          mediumRiskIssues: 1,
          lowRiskIssues: 0,
          infoIssues: 0,
          falsePositives: 0,
          pagesScanned: 25,
          requestsMade: 150,
        },
        vulnerabilities: [],
        recommendations: [],
        evidence: [],
        metadata: {
          targetInfo: {
            type: 'web-app',
            url: 'https://example.com',
          },
          testConfig: {
            target: {
              type: 'web-app',
              url: 'https://example.com',
            },
            options: {
              aggressive: false,
              stealthMode: true,
              socialEngineering: false,
              physicalTesting: false,
              complianceChecks: true,
            },
          },
          toolsUsed: ['PentX Scanner'],
          coverage: {
            urlsCovered: 25,
            endpointsTested: 10,
            parametersAnalyzed: 50,
          },
        },
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: mockResults,
          requestId: 'req-123',
          timestamp: new Date(),
        }),
      } as Response);

      const result = await service.getTestResults('test-123');

      expect(result).toEqual(mockResults);
    });
  });

  describe('waitForTestCompletion', () => {
    it('should poll until test completion', async () => {
      const testId = 'test-123';
      
      // Mock status calls - first running, then completed
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            success: true,
            data: {
              testId,
              status: 'running',
              progress: { percentage: 50 },
              vulnerabilitiesFound: 1,
              lastUpdate: new Date(),
            },
          }),
        } as Response)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            success: true,
            data: {
              testId,
              status: 'completed',
              progress: { percentage: 100 },
              vulnerabilitiesFound: 2,
              lastUpdate: new Date(),
            },
          }),
        } as Response)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            success: true,
            data: {
              testId,
              status: 'completed',
              vulnerabilities: [],
              summary: { vulnerabilitiesFound: 2 },
            },
          }),
        } as Response);

      const progressCallback = jest.fn();
      
      const result = await service.waitForTestCompletion(testId, {
        pollInterval: 100, // Fast polling for test
        maxWaitTime: 5000,
        onProgress: progressCallback,
      });

      expect(result.testId).toBe(testId);
      expect(progressCallback).toHaveBeenCalledTimes(2);
      expect(mockFetch).toHaveBeenCalledTimes(3); // 2 status calls + 1 results call
    });

    it('should timeout if test takes too long', async () => {
      const testId = 'test-123';
      
      // Mock status to always return running
      mockFetch.mockResolvedValue({
        ok: true,
        json: async () => ({
          success: true,
          data: {
            testId,
            status: 'running',
            progress: { percentage: 10 },
            vulnerabilitiesFound: 0,
            lastUpdate: new Date(),
          },
        }),
      } as Response);

      await expect(
        service.waitForTestCompletion(testId, {
          pollInterval: 100,
          maxWaitTime: 500, // Short timeout for test
        })
      ).rejects.toThrow('Test test-123 timed out');
    });

    it('should handle failed tests', async () => {
      const testId = 'test-123';
      
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: {
            testId,
            status: 'failed',
            progress: { percentage: 30 },
            vulnerabilitiesFound: 0,
            lastUpdate: new Date(),
          },
        }),
      } as Response);

      await expect(
        service.waitForTestCompletion(testId, {
          pollInterval: 100,
          maxWaitTime: 5000,
        })
      ).rejects.toThrow('Test test-123 failed');
    });
  });

  describe('Rate Limiting', () => {
    it('should handle rate limiting with retry', async () => {
      // First call returns 429, second call succeeds
      mockFetch
        .mockResolvedValueOnce({
          ok: false,
          status: 429,
          headers: new Map([['Retry-After', '1']]),
          json: async () => ({}),
        } as any)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            success: true,
            data: { status: 'healthy' },
          }),
        } as Response);

      // Mock setTimeout to resolve immediately for testing
      jest.spyOn(global, 'setTimeout').mockImplementation((callback: any) => {
        callback();
        return {} as any;
      });

      const result = await service.healthCheck();
      
      expect(result.status).toBe('healthy');
      expect(mockFetch).toHaveBeenCalledTimes(2);
    });
  });

  describe('API Key Management', () => {
    it('should update API key', () => {
      const newApiKey = 'new-api-key';
      service.updateApiKey(newApiKey);
      
      expect(service.getConfig().apiKey).toBe(newApiKey);
    });
  });
});
