import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { PentXPlugin } from '../pentx-plugin';
import { PentXService } from '../pentx-service';
import { PentXPluginConfig, PentXResults, PentXVulnerability } from '../types';
import { VulnerabilityType } from '../../../types';

// Mock the PentXService
jest.mock('../pentx-service');

describe('PentXPlugin', () => {
  let plugin: PentXPlugin;
  let mockPentXService: jest.Mocked<PentXService>;
  let config: PentXPluginConfig;

  beforeEach(() => {
    config = {
      apiKey: 'test-api-key',
      baseUrl: 'https://api.pentx.ai/v1',
      defaultTimeout: 1800000,
      maxConcurrentTests: 2,
      defaultScope: {
        depth: 'medium',
        timeLimit: 60,
      },
      defaultOptions: {
        aggressive: false,
        stealthMode: true,
        socialEngineering: false,
        physicalTesting: false,
        complianceChecks: true,
      },
    };

    plugin = new PentXPlugin(config);
    mockPentXService = plugin['pentxService'] as jest.Mocked<PentXService>;
  });

  describe('Plugin Properties', () => {
    it('should have correct plugin metadata', () => {
      expect(plugin.id).toBe('pentx');
      expect(plugin.name).toBe('PentX Autonomous AI Penetration Testing');
      expect(plugin.description).toContain('Autonomous AI-powered penetration testing');
      expect(plugin.version).toBe('1.0.0');
    });

    it('should support PentX vulnerability types', () => {
      expect(plugin.vulnerabilityTypes).toContain(VulnerabilityType.PENTX_WEB_VULNERABILITY);
      expect(plugin.vulnerabilityTypes).toContain(VulnerabilityType.PENTX_API_VULNERABILITY);
      expect(plugin.vulnerabilityTypes).toContain(VulnerabilityType.PENTX_INFRASTRUCTURE_VULNERABILITY);
      expect(plugin.vulnerabilityTypes).toContain(VulnerabilityType.PENTX_AUTHENTICATION_BYPASS);
    });
  });

  describe('generateTestCases', () => {
    it('should generate test cases for web application testing', async () => {
      const pluginConfig = { id: 'pentx', config: {} };
      const context = { url: 'https://example.com' };

      const testCases = await plugin.generateTestCases(pluginConfig, context);

      expect(testCases).toHaveLength(6); // 6 phases for web-app
      expect(testCases[0].description).toContain('web-app autonomous penetration test');
      expect(testCases[0].plugin).toBe('pentx');
      expect(testCases[0].metadata?.targetType).toBe('web-app');
    });

    it('should generate test cases for API testing', async () => {
      const pluginConfig = { id: 'pentx', config: {} };
      const context = { endpoint: 'https://api.example.com/api/v1' };

      const testCases = await plugin.generateTestCases(pluginConfig, context);

      expect(testCases).toHaveLength(6); // 6 phases for API
      expect(testCases[0].metadata?.targetType).toBe('api');
    });

    it('should generate test cases for infrastructure testing', async () => {
      const pluginConfig = { id: 'pentx', config: {} };
      const context = { ipRanges: ['***********/24'] };

      const testCases = await plugin.generateTestCases(pluginConfig, context);

      expect(testCases).toHaveLength(6); // 6 phases for infrastructure
      expect(testCases[0].metadata?.targetType).toBe('infrastructure');
    });

    it('should include expected vulnerabilities in metadata', async () => {
      const pluginConfig = { id: 'pentx', config: {} };
      const context = { url: 'https://example.com' };

      const testCases = await plugin.generateTestCases(pluginConfig, context);

      expect(testCases[0].metadata?.expectedVulnerabilities).toContain('XSS');
      expect(testCases[0].metadata?.expectedVulnerabilities).toContain('SQL Injection');
    });
  });

  describe('evaluateResult', () => {
    let mockPentXResults: PentXResults;
    let mockVulnerabilities: PentXVulnerability[];

    beforeEach(() => {
      mockVulnerabilities = [
        {
          id: 'vuln-1',
          type: 'web-vulnerability',
          severity: 'high',
          title: 'SQL Injection',
          description: 'SQL injection vulnerability found',
          impact: 'High impact vulnerability',
          recommendation: 'Use parameterized queries',
          evidence: {
            location: '/login',
            payload: "' OR 1=1 --",
            request: 'POST /login',
            response: 'Error: SQL syntax error',
          },
          references: {
            cwe: 'CWE-89',
            owasp: 'A03:2021',
          },
          discoveredAt: new Date(),
          falsePositive: false,
        },
      ];

      mockPentXResults = {
        testId: 'test-123',
        status: 'completed',
        startTime: new Date(),
        endTime: new Date(),
        duration: 300000, // 5 minutes
        summary: {
          vulnerabilitiesFound: 1,
          criticalIssues: 0,
          highRiskIssues: 1,
          mediumRiskIssues: 0,
          lowRiskIssues: 0,
          infoIssues: 0,
          falsePositives: 0,
          pagesScanned: 10,
          requestsMade: 50,
        },
        vulnerabilities: mockVulnerabilities,
        recommendations: [],
        evidence: [],
        metadata: {
          targetInfo: {
            type: 'web-app',
            url: 'https://example.com',
          },
          testConfig: {
            target: {
              type: 'web-app',
              url: 'https://example.com',
            },
            options: {
              aggressive: false,
              stealthMode: true,
              socialEngineering: false,
              physicalTesting: false,
              complianceChecks: true,
            },
          },
          toolsUsed: ['PentX AI Scanner'],
          coverage: {
            urlsCovered: 10,
            endpointsTested: 5,
            parametersAnalyzed: 20,
          },
        },
      };
    });

    it('should evaluate successful test results', async () => {
      const testCase = {
        id: 'test-case-1',
        description: 'PentX web-app test',
        input: JSON.stringify({ testId: 'test-123' }),
        plugin: 'pentx',
        metadata: {
          targetType: 'web-app',
          testPhase: 'vulnerability-scanning',
        },
      };

      const output = JSON.stringify({ testId: 'test-123' });

      mockPentXService.waitForTestCompletion.mockResolvedValue(mockPentXResults);

      const result = await plugin.evaluateResult(testCase, output);

      expect(result.passed).toBe(true);
      expect(result.vulnerabilities).toHaveLength(1);
      expect(result.vulnerabilities[0].type).toBe(VulnerabilityType.PENTX_WEB_VULNERABILITY);
      expect(result.vulnerabilities[0].severity).toBe('high');
      expect(result.metadata?.pentxTestId).toBe('test-123');
    });

    it('should handle test failures gracefully', async () => {
      const testCase = {
        id: 'test-case-1',
        description: 'PentX web-app test',
        input: JSON.stringify({ testId: 'test-123' }),
        plugin: 'pentx',
        metadata: {
          targetType: 'web-app',
          testPhase: 'vulnerability-scanning',
        },
      };

      const output = JSON.stringify({ testId: 'test-123' });

      mockPentXService.waitForTestCompletion.mockRejectedValue(new Error('Test timeout'));

      const result = await plugin.evaluateResult(testCase, output);

      expect(result.passed).toBe(false);
      expect(result.vulnerabilities).toHaveLength(0);
      expect(result.metadata?.error).toBe('Test timeout');
    });

    it('should handle invalid output format', async () => {
      const testCase = {
        id: 'test-case-1',
        description: 'PentX web-app test',
        input: 'invalid-input',
        plugin: 'pentx',
        metadata: {
          targetType: 'web-app',
          testPhase: 'vulnerability-scanning',
        },
      };

      const output = 'invalid-output';

      const result = await plugin.evaluateResult(testCase, output);

      expect(result.passed).toBe(false);
      expect(result.vulnerabilities).toHaveLength(0);
      expect(result.metadata?.error).toContain('Failed to extract PentX test ID');
    });
  });

  describe('Vulnerability Conversion', () => {
    it('should convert PentX vulnerabilities to red team format', async () => {
      const pentxVuln: PentXVulnerability = {
        id: 'vuln-1',
        type: 'web-vulnerability',
        severity: 'critical',
        title: 'Remote Code Execution',
        description: 'RCE vulnerability found',
        impact: 'Critical impact',
        recommendation: 'Update framework',
        evidence: {
          location: '/upload',
          payload: 'malicious-file.php',
        },
        references: {
          cwe: 'CWE-94',
          owasp: 'A03:2021',
        },
        discoveredAt: new Date(),
        falsePositive: false,
      };

      const converted = plugin['convertPentXVulnerabilities']([pentxVuln]);

      expect(converted).toHaveLength(1);
      expect(converted[0].type).toBe(VulnerabilityType.PENTX_WEB_VULNERABILITY);
      expect(converted[0].severity).toBe('critical');
      expect(converted[0].description).toBe('RCE vulnerability found');
      expect(converted[0].recommendation).toBe('Update framework');
      expect(converted[0].cwe).toBe('CWE-94');
      expect(converted[0].owasp).toBe('A03:2021');
    });
  });

  describe('Target Type Detection', () => {
    it('should detect web-app target type from URL', () => {
      const context = { url: 'https://example.com' };
      const targetType = plugin['determineTargetType'](context);
      expect(targetType).toBe('web-app');
    });

    it('should detect API target type from endpoint', () => {
      const context = { endpoint: 'https://api.example.com/api/v1' };
      const targetType = plugin['determineTargetType'](context);
      expect(targetType).toBe('api');
    });

    it('should detect infrastructure target type from IP ranges', () => {
      const context = { ipRanges: ['***********/24'] };
      const targetType = plugin['determineTargetType'](context);
      expect(targetType).toBe('infrastructure');
    });

    it('should default to web-app for unknown context', () => {
      const context = {};
      const targetType = plugin['determineTargetType'](context);
      expect(targetType).toBe('web-app');
    });
  });

  describe('Test Score Calculation', () => {
    it('should calculate perfect score for no vulnerabilities', () => {
      const results: PentXResults = {
        ...mockPentXResults,
        summary: {
          vulnerabilitiesFound: 0,
          criticalIssues: 0,
          highRiskIssues: 0,
          mediumRiskIssues: 0,
          lowRiskIssues: 0,
          infoIssues: 0,
          falsePositives: 0,
          pagesScanned: 10,
          requestsMade: 50,
        },
        vulnerabilities: [],
      };

      const score = plugin['calculateTestScore'](results);
      expect(score).toBe(100);
    });

    it('should calculate lower score for critical vulnerabilities', () => {
      const results: PentXResults = {
        ...mockPentXResults,
        summary: {
          vulnerabilitiesFound: 2,
          criticalIssues: 2,
          highRiskIssues: 0,
          mediumRiskIssues: 0,
          lowRiskIssues: 0,
          infoIssues: 0,
          falsePositives: 0,
          pagesScanned: 10,
          requestsMade: 50,
        },
      };

      const score = plugin['calculateTestScore'](results);
      expect(score).toBeLessThan(100);
      expect(score).toBeGreaterThanOrEqual(0);
    });
  });
});
