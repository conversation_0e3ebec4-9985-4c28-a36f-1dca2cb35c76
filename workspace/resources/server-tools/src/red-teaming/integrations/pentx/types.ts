// PentX.ai Integration Types

export interface PentXConfig {
  apiKey: string;
  baseUrl?: string;
  timeout?: number;
  retryAttempts?: number;
  rateLimitDelay?: number;
}

export interface PentXTarget {
  type: 'web-app' | 'api' | 'infrastructure' | 'network';
  url?: string;
  endpoints?: string[];
  ipRanges?: string[];
  credentials?: PentXCredentials;
  scope?: PentXScope;
}

export interface PentXCredentials {
  username?: string;
  password?: string;
  apiKey?: string;
  bearerToken?: string;
  customHeaders?: Record<string, string>;
}

export interface PentXScope {
  depth: 'shallow' | 'medium' | 'deep';
  timeLimit: number; // minutes
  excludePatterns?: string[];
  includePatterns?: string[];
  maxRequests?: number;
}

export interface PentXTestConfig {
  target: PentXTarget;
  options: {
    aggressive: boolean;
    stealthMode: boolean;
    socialEngineering: boolean;
    physicalTesting: boolean;
    complianceChecks: boolean;
  };
  notifications?: {
    webhookUrl?: string;
    email?: string;
    slackChannel?: string;
  };
}

export interface PentXTestRequest {
  name: string;
  description?: string;
  config: PentXTestConfig;
  priority?: 'low' | 'medium' | 'high' | 'critical';
  scheduledFor?: Date;
}

export interface PentXTestResponse {
  testId: string;
  status: 'queued' | 'running' | 'completed' | 'failed' | 'cancelled';
  estimatedDuration?: number;
  queuePosition?: number;
  createdAt: Date;
}

export interface PentXTestStatus {
  testId: string;
  status: 'queued' | 'running' | 'completed' | 'failed' | 'cancelled';
  progress: {
    percentage: number;
    currentPhase: string;
    phasesCompleted: string[];
    phasesRemaining: string[];
  };
  startTime?: Date;
  endTime?: Date;
  estimatedTimeRemaining?: number;
  vulnerabilitiesFound: number;
  lastUpdate: Date;
}

export interface PentXVulnerability {
  id: string;
  type: string;
  severity: 'info' | 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  impact: string;
  recommendation: string;
  evidence: {
    request?: string;
    response?: string;
    screenshot?: string;
    payload?: string;
    location?: string;
  };
  references: {
    cwe?: string;
    owasp?: string;
    cvss?: string;
    cve?: string;
  };
  discoveredAt: Date;
  verifiedAt?: Date;
  falsePositive: boolean;
}

export interface PentXRecommendation {
  id: string;
  category: 'immediate' | 'short-term' | 'long-term';
  priority: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  steps: string[];
  estimatedEffort: string;
  businessImpact: string;
}

export interface PentXEvidence {
  id: string;
  type: 'screenshot' | 'request' | 'response' | 'log' | 'file';
  title: string;
  description?: string;
  content: string;
  contentType: string;
  size: number;
  createdAt: Date;
}

export interface PentXResults {
  testId: string;
  status: 'completed' | 'failed' | 'cancelled';
  startTime: Date;
  endTime: Date;
  duration: number; // milliseconds
  summary: {
    vulnerabilitiesFound: number;
    criticalIssues: number;
    highRiskIssues: number;
    mediumRiskIssues: number;
    lowRiskIssues: number;
    infoIssues: number;
    falsePositives: number;
    pagesScanned: number;
    requestsMade: number;
  };
  vulnerabilities: PentXVulnerability[];
  recommendations: PentXRecommendation[];
  evidence: PentXEvidence[];
  metadata: {
    targetInfo: PentXTarget;
    testConfig: PentXTestConfig;
    toolsUsed: string[];
    coverage: {
      urlsCovered: number;
      endpointsTested: number;
      parametersAnalyzed: number;
    };
  };
}

export interface PentXError {
  code: string;
  message: string;
  details?: Record<string, any>;
  timestamp: Date;
}

export interface PentXAPIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: PentXError;
  requestId: string;
  timestamp: Date;
}

export interface PentXQuota {
  testsRemaining: number;
  testsUsed: number;
  resetDate: Date;
  planType: string;
  concurrentTestsLimit: number;
  currentConcurrentTests: number;
}

export interface PentXHealthCheck {
  status: 'healthy' | 'degraded' | 'unhealthy';
  version: string;
  uptime: number;
  responseTime: number;
  features: {
    webAppTesting: boolean;
    apiTesting: boolean;
    infrastructureTesting: boolean;
    networkTesting: boolean;
  };
  lastCheck: Date;
}

// Plugin-specific configuration
export interface PentXPluginConfig {
  apiKey: string;
  baseUrl?: string;
  defaultTimeout?: number;
  maxConcurrentTests?: number;
  defaultScope?: Partial<PentXScope>;
  defaultOptions?: Partial<PentXTestConfig['options']>;
  webhookUrl?: string;
  enableRealTimeUpdates?: boolean;
}

// Test case metadata for PentX integration
export interface PentXTestCaseMetadata {
  pentxTestId?: string;
  targetType: PentXTarget['type'];
  testPhase: string;
  expectedVulnerabilities?: string[];
  testDuration?: number;
  aggressiveMode?: boolean;
}

// Result mapping for integration with red-teaming framework
export interface PentXResultMapping {
  pentxVulnerabilityType: string;
  redTeamVulnerabilityType: string;
  severityMapping: Record<string, 'low' | 'medium' | 'high' | 'critical'>;
  confidenceScore: number;
}
