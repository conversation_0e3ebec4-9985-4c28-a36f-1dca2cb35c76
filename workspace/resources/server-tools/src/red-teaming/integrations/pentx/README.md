# PentX.ai Integration for Red Team Framework

This integration brings autonomous AI-powered penetration testing capabilities to the Divinci AI red-teaming framework through PentX.ai's platform.

## Overview

PentX.ai is a fully autonomous AI penetration testing platform that can automatically discover, exploit, and report security vulnerabilities across web applications, APIs, and infrastructure. This integration allows you to seamlessly incorporate PentX's autonomous testing capabilities into your existing security assessment workflows.

## Features

- 🤖 **Autonomous AI Testing**: Fully automated penetration testing with minimal human intervention
- 🌐 **Multi-Target Support**: Web applications, APIs, infrastructure, and network testing
- 🔍 **Comprehensive Coverage**: Reconnaissance, vulnerability scanning, exploitation, and reporting
- 📊 **Detailed Reporting**: Rich vulnerability reports with evidence and remediation guidance
- ⚡ **Real-time Monitoring**: Live progress tracking and status updates
- 🔧 **Flexible Configuration**: Customizable test depth, scope, and aggressiveness levels

## Installation

### Prerequisites

1. **PentX.ai Account**: Sign up at [https://pentx.ai](https://pentx.ai) and obtain an API key
2. **Node.js**: Version 16 or higher
3. **Red Team Framework**: Ensure the base red-teaming framework is installed

### Setup

1. **Install Dependencies**:

   ```bash
   npm install
   ```

2. **Configure Environment Variables**:

   ```bash
   export PENTX_API_KEY="your-pentx-api-key"
   export PENTX_BASE_URL="https://api.pentx.ai/v1"  # Optional, uses default if not set
   ```

3. **Register the Plugin**:

   ```typescript
   import { PluginManager } from "../plugins/manager";
   import { PentXPluginConfig } from "./integrations/pentx/types";

   const pluginManager = new PluginManager();

   const pentxConfig: PentXPluginConfig = {
     apiKey: process.env.PENTX_API_KEY!,
     baseUrl: process.env.PENTX_BASE_URL,
     defaultTimeout: 1800000, // 30 minutes
     maxConcurrentTests: 2,
     enableRealTimeUpdates: true,
   };

   pluginManager.registerPentXPlugin(pentxConfig);
   ```

## Quick Start

### Basic Web Application Testing

```typescript
import { RedTeamEngine } from "../core/engine";
import { ConfigTemplates } from "../core/config";

const engine = new RedTeamEngine();

// Create web app testing configuration
const config = ConfigTemplates.pentxWebApp({
  url: "https://example.com",
  purpose: "Security assessment of example web application",
  depth: "medium",
  timeLimit: 60,
  aggressive: false,
});

// Run the assessment
const report = await engine.runAssessment(config);

console.log(`Found ${report.summary.vulnerabilitiesFound} vulnerabilities`);
console.log(`Risk Level: ${report.summary.riskLevel}`);
```

### API Security Assessment

```typescript
const config = ConfigTemplates.pentxAPI({
  baseUrl: "https://api.example.com",
  endpoints: ["/v1/users", "/v1/auth", "/v1/data"],
  purpose: "API security assessment",
  depth: "deep",
  credentials: {
    bearerToken: "your-api-token",
  },
});

const report = await engine.runAssessment(config);
```

### Infrastructure Testing

```typescript
const config = ConfigTemplates.pentxInfrastructure({
  ipRanges: ["***********/24", "10.0.0.0/16"],
  purpose: "Infrastructure security assessment",
  depth: "medium",
  aggressive: false,
});

const report = await engine.runAssessment(config);
```

## Configuration Options

### Plugin Configuration

```typescript
interface PentXPluginConfig {
  apiKey: string; // Required: PentX API key
  baseUrl?: string; // Optional: API base URL
  defaultTimeout?: number; // Default test timeout (ms)
  maxConcurrentTests?: number; // Max concurrent tests
  defaultScope?: {
    depth: "shallow" | "medium" | "deep";
    timeLimit: number; // Minutes
  };
  defaultOptions?: {
    aggressive: boolean; // Aggressive testing mode
    stealthMode: boolean; // Stealth mode
    socialEngineering: boolean; // Social engineering tests
    physicalTesting: boolean; // Physical security tests
    complianceChecks: boolean; // Compliance validation
  };
  enableRealTimeUpdates?: boolean; // Real-time progress updates
}
```

### Test Configuration

```typescript
interface PentXTestConfig {
  target: {
    type: "web-app" | "api" | "infrastructure" | "network";
    url?: string; // For web-app and API
    endpoints?: string[]; // For API testing
    ipRanges?: string[]; // For infrastructure/network
    credentials?: {
      username?: string;
      password?: string;
      apiKey?: string;
      bearerToken?: string;
    };
  };
  scope: {
    depth: "shallow" | "medium" | "deep";
    timeLimit: number; // Minutes
    excludePatterns?: string[]; // URLs/IPs to exclude
    includePatterns?: string[]; // URLs/IPs to include
  };
  options: {
    aggressive: boolean; // More thorough but potentially disruptive
    stealthMode: boolean; // Avoid detection
    socialEngineering: boolean; // Include social engineering tests
    physicalTesting: boolean; // Include physical security tests
    complianceChecks: boolean; // Validate compliance requirements
  };
}
```

## Configuration Templates

The integration provides several pre-configured templates:

### Web Application Templates

```typescript
// Basic web app testing
ConfigTemplates.pentxWebApp({
  url: "https://example.com",
  depth: "medium",
  timeLimit: 60,
});

// Comprehensive web app testing
ConfigTemplates.pentxWebApp({
  url: "https://example.com",
  depth: "deep",
  timeLimit: 120,
  aggressive: true,
});
```

### API Testing Templates

```typescript
// Basic API testing
ConfigTemplates.pentxAPI({
  baseUrl: "https://api.example.com",
  endpoints: ["/v1/users", "/v1/auth"],
  depth: "medium",
});

// Authenticated API testing
ConfigTemplates.pentxAPI({
  baseUrl: "https://api.example.com",
  credentials: {
    bearerToken: "your-token",
  },
  depth: "deep",
});
```

### Infrastructure Templates

```typescript
// Network range testing
ConfigTemplates.pentxInfrastructure({
  ipRanges: ["***********/24"],
  depth: "medium",
  timeLimit: 90,
});

// Comprehensive infrastructure testing
ConfigTemplates.pentxInfrastructure({
  ipRanges: ["10.0.0.0/8", "**********/12"],
  depth: "deep",
  timeLimit: 180,
  aggressive: true,
});
```

### Comprehensive Testing

```typescript
// Multi-target comprehensive testing
ConfigTemplates.pentxComprehensive({
  url: "https://example.com",
  ipRanges: ["***********/24"],
  timeLimit: 240,
  aggressive: false,
});
```

## Monitoring and Progress Tracking

### Real-time Progress Updates

```typescript
const engine = new RedTeamEngine();

// Add event handlers for monitoring
engine.addEventHandler((event) => {
  switch (event.type) {
    case "test_started":
      console.log("🟡 Test started:", event.data.config.purpose);
      break;
    case "vulnerability_found":
      console.log("🔴 Vulnerability found:", event.data.vulnerabilities.length);
      break;
    case "test_completed":
      console.log("🟢 Test completed successfully");
      break;
    case "error":
      console.error("❌ Test error:", event.data.error);
      break;
  }
});
```

### Custom Progress Callbacks

```typescript
const pentxService = new PentXService(config);

await pentxService.waitForTestCompletion(testId, {
  pollInterval: 30000, // 30 seconds
  maxWaitTime: 3600000, // 1 hour
  onProgress: (status) => {
    console.log(`Progress: ${status.progress.percentage}%`);
    console.log(`Phase: ${status.progress.currentPhase}`);
    console.log(`Vulnerabilities: ${status.vulnerabilitiesFound}`);
  },
});
```

## Report Generation

### Enhanced Reports with PentX Data

```typescript
const reportGenerator = engine.reportGenerator;

// Generate enhanced report with PentX details
const enhancedReport = await reportGenerator.exportPentXReport(
  report,
  "markdown"
);

// Save to file
import fs from "fs";
fs.writeFileSync("pentx-security-report.md", enhancedReport);
```

### Report Formats

- **Markdown**: Human-readable format with PentX-specific sections
- **HTML**: Rich web-based report with interactive elements
- **JSON**: Machine-readable format for integration
- **YAML**: Configuration-friendly format

## Error Handling

### Common Error Scenarios

```typescript
try {
  const report = await engine.runAssessment(config);
} catch (error) {
  if (error.message.includes("API key")) {
    console.error("Invalid or missing PentX API key");
  } else if (error.message.includes("timeout")) {
    console.error("Test timed out - consider increasing timeLimit");
  } else if (error.message.includes("rate limit")) {
    console.error("API rate limit exceeded - wait before retrying");
  } else {
    console.error("Unexpected error:", error.message);
  }
}
```

### Retry Logic

```typescript
const pentxService = new PentXService({
  apiKey: "your-key",
  retryAttempts: 3, // Retry failed requests
  rateLimitDelay: 60000, // Wait 60s on rate limit
});
```

## Best Practices

### Security Considerations

1. **API Key Security**: Store API keys securely using environment variables or secret management
2. **Target Authorization**: Ensure you have permission to test all targets
3. **Scope Limitation**: Use `excludePatterns` to avoid testing sensitive areas
4. **Rate Limiting**: Respect API rate limits and implement backoff strategies

### Performance Optimization

1. **Concurrent Testing**: Limit `maxConcurrentTests` based on your API quota
2. **Test Depth**: Use appropriate depth levels - `shallow` for quick scans, `deep` for comprehensive testing
3. **Time Limits**: Set realistic `timeLimit` values based on target complexity
4. **Stealth Mode**: Enable for production environments to minimize impact

### Monitoring and Alerting

1. **Progress Tracking**: Implement progress callbacks for long-running tests
2. **Error Handling**: Set up proper error handling and alerting
3. **Result Storage**: Store test results for historical analysis
4. **Compliance**: Ensure testing complies with organizational policies

## Troubleshooting

### Common Issues

1. **Authentication Errors**: Verify API key is correct and has sufficient permissions
2. **Network Timeouts**: Check network connectivity and increase timeout values
3. **Target Unreachable**: Verify target URLs/IPs are accessible from test environment
4. **Rate Limiting**: Implement exponential backoff and respect rate limits

### Debug Mode

```typescript
const config: PentXPluginConfig = {
  apiKey: "your-key",
  // ... other config
};

// Enable debug logging
process.env.DEBUG = "pentx:*";
```

## Support

- **Documentation**: [PentX.ai Documentation](https://docs.pentx.ai)
- **API Reference**: [PentX API Reference](https://api.pentx.ai/docs)
- **Support**: Contact PentX support for API-related issues
- **Integration Issues**: File issues in the red-teaming framework repository

## Examples

See the `examples/usage-examples.ts` file for comprehensive usage examples including:

- Basic web application testing
- API security assessment
- Infrastructure penetration testing
- Comprehensive multi-target testing
- Scheduled automated testing
- Error handling and monitoring

## Testing

Run the test suite:

```bash
# Run all PentX integration tests
npm test -- --testPathPattern=pentx

# Run specific test files
npm test pentx-plugin.test.ts
npm test pentx-service.test.ts

# Run with coverage
npm test -- --coverage --testPathPattern=pentx
```

## Web Interface

The integration includes a React-based web interface for managing PentX tests:

- **Dashboard**: View test status, results, and statistics
- **Test Creation**: Configure and launch new autonomous penetration tests
- **Real-time Monitoring**: Track test progress and receive updates
- **Report Viewing**: Access detailed vulnerability reports and recommendations

Access the PentX dashboard at `/security/pentx` in your web application.

## API Endpoints

The integration exposes REST API endpoints for programmatic access:

```
GET    /api/security/pentx/tests          # List tests
POST   /api/security/pentx/tests          # Create new test
GET    /api/security/pentx/tests/:id      # Get test details
POST   /api/security/pentx/tests/:id/cancel # Cancel test
GET    /api/security/pentx/config         # Get configuration
PUT    /api/security/pentx/config         # Update configuration
```

## License

This integration is part of the Divinci AI red-teaming framework. See the main project license for details.
