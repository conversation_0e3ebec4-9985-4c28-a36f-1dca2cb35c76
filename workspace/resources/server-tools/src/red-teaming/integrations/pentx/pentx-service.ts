import {
  PentXConfig,
  PentXTestRequest,
  PentXTestResponse,
  PentXTestStatus,
  PentXResults,
  PentXAPIResponse,
  PentXQuota,
  PentXHealthCheck,
  PentXError,
} from './types';

/**
 * PentX.ai API Service
 * Handles communication with the PentX.ai autonomous penetration testing platform
 */
export class PentXService {
  private config: PentXConfig;
  private baseUrl: string;
  private headers: Record<string, string>;

  constructor(config: PentXConfig) {
    this.config = config;
    this.baseUrl = config.baseUrl || 'https://api.pentx.ai/v1';
    this.headers = {
      'Authorization': `Bearer ${config.apiKey}`,
      'Content-Type': 'application/json',
      'User-Agent': 'Divinci-AI-RedTeam/1.0.0',
    };
  }

  /**
   * Health check for PentX API
   */
  async healthCheck(): Promise<PentXHealthCheck> {
    try {
      const response = await this.makeRequest<PentXHealthCheck>('GET', '/health');
      return response.data!;
    } catch (error) {
      throw new Error(`PentX health check failed: ${(error as Error).message}`);
    }
  }

  /**
   * Get current quota information
   */
  async getQuota(): Promise<PentXQuota> {
    try {
      const response = await this.makeRequest<PentXQuota>('GET', '/quota');
      return response.data!;
    } catch (error) {
      throw new Error(`Failed to get PentX quota: ${(error as Error).message}`);
    }
  }

  /**
   * Create a new penetration test
   */
  async createTest(request: PentXTestRequest): Promise<PentXTestResponse> {
    try {
      // Validate request
      this.validateTestRequest(request);

      const response = await this.makeRequest<PentXTestResponse>('POST', '/tests', request);
      return response.data!;
    } catch (error) {
      throw new Error(`Failed to create PentX test: ${(error as Error).message}`);
    }
  }

  /**
   * Get test status
   */
  async getTestStatus(testId: string): Promise<PentXTestStatus> {
    try {
      const response = await this.makeRequest<PentXTestStatus>('GET', `/tests/${testId}/status`);
      return response.data!;
    } catch (error) {
      throw new Error(`Failed to get test status: ${(error as Error).message}`);
    }
  }

  /**
   * Get test results
   */
  async getTestResults(testId: string): Promise<PentXResults> {
    try {
      const response = await this.makeRequest<PentXResults>('GET', `/tests/${testId}/results`);
      return response.data!;
    } catch (error) {
      throw new Error(`Failed to get test results: ${(error as Error).message}`);
    }
  }

  /**
   * Cancel a running test
   */
  async cancelTest(testId: string): Promise<void> {
    try {
      await this.makeRequest('DELETE', `/tests/${testId}`);
    } catch (error) {
      throw new Error(`Failed to cancel test: ${(error as Error).message}`);
    }
  }

  /**
   * Wait for test completion with polling
   */
  async waitForTestCompletion(
    testId: string,
    options: {
      pollInterval?: number;
      maxWaitTime?: number;
      onProgress?: (status: PentXTestStatus) => void;
    } = {}
  ): Promise<PentXResults> {
    const {
      pollInterval = 30000, // 30 seconds
      maxWaitTime = 3600000, // 1 hour
      onProgress,
    } = options;

    const startTime = Date.now();
    
    while (Date.now() - startTime < maxWaitTime) {
      const status = await this.getTestStatus(testId);
      
      if (onProgress) {
        onProgress(status);
      }

      if (status.status === 'completed') {
        return await this.getTestResults(testId);
      }

      if (status.status === 'failed' || status.status === 'cancelled') {
        throw new Error(`Test ${testId} ${status.status}`);
      }

      // Wait before next poll
      await this.sleep(pollInterval);
    }

    throw new Error(`Test ${testId} timed out after ${maxWaitTime}ms`);
  }

  /**
   * Validate test request
   */
  private validateTestRequest(request: PentXTestRequest): void {
    if (!request.name || request.name.trim().length === 0) {
      throw new Error('Test name is required');
    }

    if (!request.config.target) {
      throw new Error('Test target is required');
    }

    const { target } = request.config;

    switch (target.type) {
      case 'web-app':
        if (!target.url) {
          throw new Error('URL is required for web application testing');
        }
        if (!this.isValidUrl(target.url)) {
          throw new Error('Invalid URL format');
        }
        break;

      case 'api':
        if (!target.url && !target.endpoints?.length) {
          throw new Error('URL or endpoints are required for API testing');
        }
        break;

      case 'infrastructure':
      case 'network':
        if (!target.ipRanges?.length) {
          throw new Error('IP ranges are required for infrastructure/network testing');
        }
        break;

      default:
        throw new Error(`Unsupported target type: ${target.type}`);
    }
  }

  /**
   * Make HTTP request to PentX API
   */
  private async makeRequest<T = any>(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE',
    endpoint: string,
    data?: any
  ): Promise<PentXAPIResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    const options: RequestInit = {
      method,
      headers: this.headers,
      body: data ? JSON.stringify(data) : undefined,
    };

    let response: Response;
    let retryCount = 0;
    const maxRetries = this.config.retryAttempts || 3;

    while (retryCount <= maxRetries) {
      try {
        response = await fetch(url, options);
        
        if (response.status === 429) {
          // Rate limited - wait and retry
          const retryAfter = parseInt(response.headers.get('Retry-After') || '60');
          await this.sleep(retryAfter * 1000);
          retryCount++;
          continue;
        }

        break;
      } catch (error) {
        if (retryCount === maxRetries) {
          throw new Error(`Network error after ${maxRetries} retries: ${(error as Error).message}`);
        }
        retryCount++;
        await this.sleep(1000 * Math.pow(2, retryCount)); // Exponential backoff
      }
    }

    const responseData = await response.json() as PentXAPIResponse<T>;

    if (!response.ok) {
      const error: PentXError = responseData.error || {
        code: response.status.toString(),
        message: response.statusText,
        timestamp: new Date(),
      };
      throw new Error(`PentX API error (${error.code}): ${error.message}`);
    }

    return responseData;
  }

  /**
   * Validate URL format
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Sleep utility
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get service configuration
   */
  getConfig(): PentXConfig {
    return { ...this.config };
  }

  /**
   * Update API key
   */
  updateApiKey(apiKey: string): void {
    this.config.apiKey = apiKey;
    this.headers['Authorization'] = `Bearer ${apiKey}`;
  }
}
