import { Plugin } from "../types";
import { BasePlugin } from "./base";
import { PentXPluginConfig } from "../integrations/pentx/types";

// Import built-in plugins
import { PromptInjectionPlugin } from "./rag/prompt-injection";
import { ContextInjectionPlugin } from "./rag/context-injection";
import { DataExfiltrationPlugin } from "./rag/data-exfiltration";
import { PIILeakPlugin } from "./common/pii-leak";
import { HarmfulContentPlugin } from "./common/harmful-content";
import { RBACPlugin } from "./agent/rbac";
import { PrivilegeEscalationPlugin } from "./agent/privilege-escalation";
import { MemoryPoisoningPlugin } from "./agent/memory-poisoning";
import { ToolManipulationPlugin } from "./agent/tool-manipulation";
import { PolicyPlugin } from "./common/policy";
import { PentXPlugin } from "./pentx/pentx-plugin";

/**
 * Plugin manager for red teaming plugins
 */
export class PluginManager {
  private plugins: Map<string, Plugin> = new Map();
  private initialized = false;

  constructor() {
    this.initializeBuiltInPlugins();
  }

  /**
   * Initialize built-in plugins
   */
  private initializeBuiltInPlugins(): void {
    if (this.initialized) return;

    // RAG-specific plugins
    this.registerPlugin(new PromptInjectionPlugin());
    this.registerPlugin(new ContextInjectionPlugin());
    this.registerPlugin(new DataExfiltrationPlugin());

    // Agent-specific plugins
    this.registerPlugin(new RBACPlugin());
    this.registerPlugin(new PrivilegeEscalationPlugin());
    this.registerPlugin(new MemoryPoisoningPlugin());
    this.registerPlugin(new ToolManipulationPlugin());

    // Common plugins
    this.registerPlugin(new PIILeakPlugin());
    this.registerPlugin(new HarmfulContentPlugin());
    this.registerPlugin(new PolicyPlugin());

    this.initialized = true;
  }

  /**
   * Register a plugin
   */
  registerPlugin(plugin: Plugin): void {
    if (this.plugins.has(plugin.id)) {
      throw new Error(`Plugin with ID '${plugin.id}' is already registered`);
    }

    this.plugins.set(plugin.id, plugin);
  }

  /**
   * Register PentX plugin with configuration
   */
  registerPentXPlugin(config: PentXPluginConfig): void {
    if (this.plugins.has("pentx")) {
      throw new Error("PentX plugin is already registered");
    }

    const pentxPlugin = new PentXPlugin(config);
    this.registerPlugin(pentxPlugin);
  }

  /**
   * Get a plugin by ID
   */
  async getPlugin(id: string): Promise<Plugin> {
    const plugin = this.plugins.get(id);
    if (!plugin) {
      throw new Error(
        `Plugin '${id}' not found. Available plugins: ${Array.from(
          this.plugins.keys()
        ).join(", ")}`
      );
    }
    return plugin;
  }

  /**
   * Get all registered plugins
   */
  getAllPlugins(): Plugin[] {
    return Array.from(this.plugins.values());
  }

  /**
   * Get plugins by vulnerability type
   */
  getPluginsByVulnerabilityType(vulnerabilityType: string): Plugin[] {
    return this.getAllPlugins().filter((plugin) =>
      plugin.vulnerabilityTypes.includes(vulnerabilityType as any)
    );
  }

  /**
   * Get plugin IDs
   */
  getPluginIds(): string[] {
    return Array.from(this.plugins.keys());
  }

  /**
   * Check if plugin exists
   */
  hasPlugin(id: string): boolean {
    return this.plugins.has(id);
  }

  /**
   * Unregister a plugin
   */
  unregisterPlugin(id: string): boolean {
    return this.plugins.delete(id);
  }

  /**
   * Get plugin info
   */
  getPluginInfo(id: string): {
    id: string;
    name: string;
    description: string;
    version: string;
    vulnerabilityTypes: string[];
  } | null {
    const plugin = this.plugins.get(id);
    if (!plugin) return null;

    return {
      id: plugin.id,
      name: plugin.name,
      description: plugin.description,
      version: plugin.version,
      vulnerabilityTypes: plugin.vulnerabilityTypes,
    };
  }

  /**
   * Get all plugin info
   */
  getAllPluginInfo(): Array<{
    id: string;
    name: string;
    description: string;
    version: string;
    vulnerabilityTypes: string[];
  }> {
    return this.getAllPlugins().map((plugin) => ({
      id: plugin.id,
      name: plugin.name,
      description: plugin.description,
      version: plugin.version,
      vulnerabilityTypes: plugin.vulnerabilityTypes,
    }));
  }

  /**
   * Load external plugin from file
   */
  async loadExternalPlugin(pluginPath: string): Promise<void> {
    try {
      // Dynamic import for external plugins
      const pluginModule = await import(pluginPath);
      const PluginClass = pluginModule.default || pluginModule.Plugin;

      if (!PluginClass) {
        throw new Error(
          `No default export or Plugin export found in ${pluginPath}`
        );
      }

      const plugin = new PluginClass();

      // Validate plugin implements the interface
      if (!this.isValidPlugin(plugin)) {
        throw new Error(
          `Plugin from ${pluginPath} does not implement the Plugin interface correctly`
        );
      }

      this.registerPlugin(plugin);
    } catch (error) {
      throw new Error(
        `Failed to load external plugin from ${pluginPath}: ${
          (error as Error).message
        }`
      );
    }
  }

  /**
   * Validate plugin implementation
   */
  private isValidPlugin(plugin: any): plugin is Plugin {
    return (
      typeof plugin.id === "string" &&
      typeof plugin.name === "string" &&
      typeof plugin.description === "string" &&
      typeof plugin.version === "string" &&
      Array.isArray(plugin.vulnerabilityTypes) &&
      typeof plugin.generateTestCases === "function" &&
      typeof plugin.evaluateResult === "function"
    );
  }

  /**
   * Get plugin statistics
   */
  getStatistics(): {
    totalPlugins: number;
    pluginsByType: Record<string, number>;
    vulnerabilityTypesCovered: string[];
  } {
    const plugins = this.getAllPlugins();
    const vulnerabilityTypes = new Set<string>();
    const pluginsByType: Record<string, number> = {};

    for (const plugin of plugins) {
      // Categorize plugins by their primary focus
      const category = this.categorizePlugin(plugin);
      pluginsByType[category] = (pluginsByType[category] || 0) + 1;

      // Collect all vulnerability types
      plugin.vulnerabilityTypes.forEach((type) => vulnerabilityTypes.add(type));
    }

    return {
      totalPlugins: plugins.length,
      pluginsByType,
      vulnerabilityTypesCovered: Array.from(vulnerabilityTypes),
    };
  }

  /**
   * Categorize plugin by its focus area
   */
  private categorizePlugin(plugin: Plugin): string {
    const id = plugin.id.toLowerCase();

    if (
      id.includes("rag") ||
      ["prompt-injection", "context-injection", "data-exfiltration"].includes(
        id
      )
    ) {
      return "RAG";
    }

    if (
      id.includes("agent") ||
      [
        "rbac",
        "privilege-escalation",
        "memory-poisoning",
        "tool-manipulation",
      ].includes(id)
    ) {
      return "Agent";
    }

    if (
      ["pii-leak", "harmful-content", "policy", "hallucination"].includes(id)
    ) {
      return "Common";
    }

    return "Other";
  }

  /**
   * Validate plugin configuration
   */
  validatePluginConfig(pluginId: string, config: any): boolean {
    const plugin = this.plugins.get(pluginId);
    if (!plugin) {
      throw new Error(`Plugin '${pluginId}' not found`);
    }

    // Basic validation - plugins can override this
    if (config && typeof config !== "object") {
      return false;
    }

    return true;
  }
}
