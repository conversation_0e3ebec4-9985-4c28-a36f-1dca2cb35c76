import { BasePlugin } from '../base';
import {
  PluginConfig,
  TestCase,
  TestResult,
  VulnerabilityType,
  Vulnerability,
} from '../../types';
import { PentXService } from '../../integrations/pentx/pentx-service';
import {
  PentXPluginConfig,
  PentXTestCaseMetadata,
  PentXTarget,
  PentXTestRequest,
  PentXResults,
  PentXVulnerability,
  PentXResultMapping,
} from '../../integrations/pentx/types';

/**
 * PentX Autonomous AI Penetration Testing Plugin
 * Integrates PentX.ai autonomous penetration testing capabilities
 */
export class PentXPlugin extends BasePlugin {
  readonly id = 'pentx';
  readonly name = 'PentX Autonomous AI Penetration Testing';
  readonly description = 'Autonomous AI-powered penetration testing using PentX.ai platform';
  readonly version = '1.0.0';
  readonly vulnerabilityTypes = [
    VulnerabilityType.PENTX_WEB_VULNERABILITY,
    VulnerabilityType.PENTX_API_VULNERABILITY,
    VulnerabilityType.PENTX_INFRASTRUCTURE_VULNERABILITY,
    VulnerabilityType.PENTX_NETWORK_VULNERABILITY,
    VulnerabilityType.PENTX_AUTHENTICATION_BYPASS,
    VulnerabilityType.PENTX_AUTHORIZATION_FLAW,
    VulnerabilityType.PENTX_INJECTION_ATTACK,
    VulnerabilityType.PENTX_CRYPTOGRAPHIC_WEAKNESS,
    VulnerabilityType.PENTX_CONFIGURATION_ERROR,
    VulnerabilityType.PENTX_BUSINESS_LOGIC_FLAW,
  ];

  private pentxService: PentXService;
  private pluginConfig: PentXPluginConfig;

  constructor(pluginConfig: PentXPluginConfig) {
    super();
    this.pluginConfig = pluginConfig;
    this.pentxService = new PentXService({
      apiKey: pluginConfig.apiKey,
      baseUrl: pluginConfig.baseUrl,
      timeout: pluginConfig.defaultTimeout,
      retryAttempts: 3,
    });
  }

  /**
   * Generate test cases for PentX autonomous penetration testing
   */
  async generateTestCases(config: PluginConfig, context?: any): Promise<TestCase[]> {
    this.logger.info('Generating PentX autonomous penetration test cases');

    const testCases: TestCase[] = [];
    const pentxConfig = config.config as Partial<PentXPluginConfig>;

    // Determine target type and configuration
    const targetType = this.determineTargetType(context);
    const target = this.buildTarget(context, targetType);

    // Generate test cases for different testing phases
    const testPhases = this.getTestPhases(targetType);

    for (const phase of testPhases) {
      const testCase = this.createTestCase(
        `PentX ${targetType} autonomous penetration test - ${phase}`,
        this.buildTestInput(target, phase),
        `Should identify security vulnerabilities in ${phase} phase`,
        {
          pentxTestId: undefined, // Will be set during execution
          targetType,
          testPhase: phase,
          expectedVulnerabilities: this.getExpectedVulnerabilities(targetType, phase),
          testDuration: pentxConfig.defaultTimeout || 1800000, // 30 minutes default
          aggressiveMode: pentxConfig.defaultOptions?.aggressive || false,
        } as PentXTestCaseMetadata
      );

      testCases.push(testCase);
    }

    this.logger.info(`Generated ${testCases.length} PentX test cases`);
    return testCases;
  }

  /**
   * Evaluate the result of a PentX test case
   */
  async evaluateResult(testCase: TestCase, output: string): Promise<TestResult> {
    this.logger.info(`Evaluating PentX test case: ${testCase.id}`);

    const metadata = testCase.metadata as PentXTestCaseMetadata;
    
    try {
      // Parse the output to get PentX test ID
      const pentxTestId = this.extractPentXTestId(output);
      
      if (!pentxTestId) {
        return this.createTestResult(
          testCase,
          output,
          false,
          [],
          0,
          { error: 'Failed to extract PentX test ID from output' }
        );
      }

      // Wait for test completion and get results
      const results = await this.pentxService.waitForTestCompletion(pentxTestId, {
        pollInterval: 30000, // 30 seconds
        maxWaitTime: metadata.testDuration || 1800000, // 30 minutes
        onProgress: (status) => {
          this.logger.info(`PentX test ${pentxTestId} progress: ${status.progress.percentage}%`);
        },
      });

      // Convert PentX results to red team vulnerabilities
      const vulnerabilities = this.convertPentXVulnerabilities(results.vulnerabilities);

      // Calculate overall score based on vulnerabilities found
      const score = this.calculateTestScore(results);

      // Determine if test passed (found expected vulnerabilities)
      const passed = this.evaluateTestSuccess(results, metadata);

      return this.createTestResult(
        testCase,
        this.formatResultOutput(results),
        passed,
        vulnerabilities,
        score,
        {
          pentxTestId,
          pentxResults: results,
          vulnerabilitiesFound: results.vulnerabilities.length,
          testDuration: results.duration,
        }
      );

    } catch (error) {
      this.logger.error(`PentX test evaluation failed: ${(error as Error).message}`);
      
      return this.createTestResult(
        testCase,
        output,
        false,
        [],
        0,
        { error: (error as Error).message }
      );
    }
  }

  /**
   * Determine target type from context
   */
  private determineTargetType(context: any): PentXTarget['type'] {
    if (context?.target?.type) {
      return context.target.type;
    }

    if (context?.endpoint || context?.url) {
      return context.endpoint?.includes('/api/') ? 'api' : 'web-app';
    }

    if (context?.ipRanges || context?.infrastructure) {
      return 'infrastructure';
    }

    // Default to web application testing
    return 'web-app';
  }

  /**
   * Build PentX target configuration
   */
  private buildTarget(context: any, targetType: PentXTarget['type']): PentXTarget {
    const target: PentXTarget = {
      type: targetType,
      scope: this.pluginConfig.defaultScope,
    };

    switch (targetType) {
      case 'web-app':
        target.url = context?.url || context?.endpoint || 'https://example.com';
        break;

      case 'api':
        target.url = context?.baseUrl || context?.endpoint;
        target.endpoints = context?.endpoints || ['/api/v1'];
        break;

      case 'infrastructure':
      case 'network':
        target.ipRanges = context?.ipRanges || ['***********/24'];
        break;
    }

    if (context?.credentials) {
      target.credentials = context.credentials;
    }

    return target;
  }

  /**
   * Get test phases for target type
   */
  private getTestPhases(targetType: PentXTarget['type']): string[] {
    const commonPhases = ['reconnaissance', 'vulnerability-scanning', 'exploitation'];

    switch (targetType) {
      case 'web-app':
        return [...commonPhases, 'authentication-testing', 'session-management', 'input-validation'];

      case 'api':
        return [...commonPhases, 'authentication-testing', 'authorization-testing', 'data-validation'];

      case 'infrastructure':
        return [...commonPhases, 'service-enumeration', 'privilege-escalation', 'lateral-movement'];

      case 'network':
        return [...commonPhases, 'port-scanning', 'service-detection', 'network-mapping'];

      default:
        return commonPhases;
    }
  }

  /**
   * Build test input for PentX
   */
  private buildTestInput(target: PentXTarget, phase: string): string {
    const testRequest: PentXTestRequest = {
      name: `Autonomous Penetration Test - ${phase}`,
      description: `PentX autonomous AI penetration testing for ${target.type} target in ${phase} phase`,
      config: {
        target,
        options: {
          aggressive: this.pluginConfig.defaultOptions?.aggressive || false,
          stealthMode: true,
          socialEngineering: false,
          physicalTesting: false,
          complianceChecks: true,
        },
      },
      priority: 'medium',
    };

    return JSON.stringify(testRequest);
  }

  /**
   * Get expected vulnerabilities for target type and phase
   */
  private getExpectedVulnerabilities(targetType: PentXTarget['type'], phase: string): string[] {
    const vulnerabilityMap: Record<string, string[]> = {
      'web-app': ['XSS', 'SQL Injection', 'CSRF', 'Authentication Bypass'],
      'api': ['Broken Authentication', 'Excessive Data Exposure', 'Injection Attacks'],
      'infrastructure': ['Unpatched Services', 'Weak Credentials', 'Misconfigurations'],
      'network': ['Open Ports', 'Weak Protocols', 'Network Segmentation Issues'],
    };

    return vulnerabilityMap[targetType] || [];
  }

  /**
   * Extract PentX test ID from output
   */
  private extractPentXTestId(output: string): string | null {
    try {
      const parsed = JSON.parse(output);
      return parsed.testId || null;
    } catch {
      // Try regex extraction as fallback
      const match = output.match(/testId["\s]*:["\s]*([a-zA-Z0-9-]+)/);
      return match ? match[1] : null;
    }
  }

  /**
   * Convert PentX vulnerabilities to red team format
   */
  private convertPentXVulnerabilities(pentxVulns: PentXVulnerability[]): Vulnerability[] {
    return pentxVulns.map(vuln => {
      const mapping = this.getVulnerabilityMapping(vuln.type);
      
      return this.createVulnerability(
        mapping.redTeamVulnerabilityType as VulnerabilityType,
        mapping.severityMapping[vuln.severity] || vuln.severity as any,
        vuln.description,
        this.formatEvidence(vuln),
        vuln.recommendation,
        vuln.references.cwe,
        vuln.references.owasp
      );
    });
  }

  /**
   * Get vulnerability type mapping
   */
  private getVulnerabilityMapping(pentxType: string): PentXResultMapping {
    const mappings: Record<string, PentXResultMapping> = {
      'web-vulnerability': {
        pentxVulnerabilityType: pentxType,
        redTeamVulnerabilityType: VulnerabilityType.PENTX_WEB_VULNERABILITY,
        severityMapping: { info: 'low', low: 'low', medium: 'medium', high: 'high', critical: 'critical' },
        confidenceScore: 0.9,
      },
      'api-vulnerability': {
        pentxVulnerabilityType: pentxType,
        redTeamVulnerabilityType: VulnerabilityType.PENTX_API_VULNERABILITY,
        severityMapping: { info: 'low', low: 'low', medium: 'medium', high: 'high', critical: 'critical' },
        confidenceScore: 0.9,
      },
      // Add more mappings as needed
    };

    return mappings[pentxType] || {
      pentxVulnerabilityType: pentxType,
      redTeamVulnerabilityType: VulnerabilityType.PENTX_WEB_VULNERABILITY,
      severityMapping: { info: 'low', low: 'low', medium: 'medium', high: 'high', critical: 'critical' },
      confidenceScore: 0.8,
    };
  }

  /**
   * Format evidence from PentX vulnerability
   */
  private formatEvidence(vuln: PentXVulnerability): string {
    const evidence = [];
    
    if (vuln.evidence.location) {
      evidence.push(`Location: ${vuln.evidence.location}`);
    }
    
    if (vuln.evidence.payload) {
      evidence.push(`Payload: ${vuln.evidence.payload}`);
    }
    
    if (vuln.evidence.request) {
      evidence.push(`Request: ${vuln.evidence.request.substring(0, 500)}...`);
    }

    return evidence.join('\n');
  }

  /**
   * Calculate test score based on results
   */
  private calculateTestScore(results: PentXResults): number {
    const { summary } = results;
    const totalIssues = summary.vulnerabilitiesFound;
    
    if (totalIssues === 0) return 100; // Perfect score if no vulnerabilities
    
    // Score based on severity distribution
    const severityWeights = {
      critical: 0.4,
      high: 0.3,
      medium: 0.2,
      low: 0.1,
    };

    const weightedScore = (
      summary.criticalIssues * severityWeights.critical +
      summary.highRiskIssues * severityWeights.high +
      summary.mediumRiskIssues * severityWeights.medium +
      summary.lowRiskIssues * severityWeights.low
    ) / totalIssues;

    return Math.max(0, 100 - (weightedScore * 100));
  }

  /**
   * Evaluate if test was successful
   */
  private evaluateTestSuccess(results: PentXResults, metadata: PentXTestCaseMetadata): boolean {
    // Test is considered successful if it completed without errors
    // and found vulnerabilities (if expected) or confirmed security (if no vulnerabilities expected)
    return results.status === 'completed' && results.vulnerabilities.length >= 0;
  }

  /**
   * Format result output for reporting
   */
  private formatResultOutput(results: PentXResults): string {
    return JSON.stringify({
      testId: results.testId,
      status: results.status,
      duration: results.duration,
      summary: results.summary,
      vulnerabilitiesFound: results.vulnerabilities.length,
      recommendations: results.recommendations.length,
    }, null, 2);
  }
}
