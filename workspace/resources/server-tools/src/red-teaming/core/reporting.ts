import {
  RedTeamReport,
  TestResult,
  Vulnerability,
  VulnerabilityType,
  RedTeamConfig,
} from "../types";
import { PentXResults, PentXVulnerability } from "../integrations/pentx/types";

/**
 * Report generator for red team assessments
 */
export class ReportGenerator {
  /**
   * Generate comprehensive red team report
   */
  async generate(data: {
    testResults: TestResult[];
    config: RedTeamConfig;
    duration: number;
  }): Promise<RedTeamReport> {
    const { testResults, config, duration } = data;

    const summary = this.generateSummary(testResults);
    const vulnerabilities = this.extractVulnerabilities(testResults);
    const recommendations = this.generateRecommendations(
      vulnerabilities,
      config
    );

    return {
      summary,
      vulnerabilities,
      testResults,
      recommendations,
      metadata: {
        timestamp: new Date(),
        duration,
        config,
        version: "1.0.0",
      },
    };
  }

  /**
   * Generate summary statistics
   */
  private generateSummary(testResults: TestResult[]): RedTeamReport["summary"] {
    const totalTests = testResults.length;
    const passedTests = testResults.filter((r) => r.passed).length;
    const failedTests = totalTests - passedTests;
    const vulnerabilitiesFound = testResults.reduce(
      (sum, r) => sum + r.vulnerabilities.length,
      0
    );

    // Calculate overall score (0-100, higher is better)
    const overallScore =
      totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0;

    // Determine risk level based on vulnerabilities
    const riskLevel = this.calculateRiskLevel(testResults);

    return {
      totalTests,
      passedTests,
      failedTests,
      vulnerabilitiesFound,
      overallScore,
      riskLevel,
    };
  }

  /**
   * Extract all vulnerabilities from test results
   */
  private extractVulnerabilities(testResults: TestResult[]): Vulnerability[] {
    const vulnerabilities: Vulnerability[] = [];

    for (const result of testResults) {
      vulnerabilities.push(...result.vulnerabilities);
    }

    // Sort by severity (critical first)
    const severityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
    vulnerabilities.sort(
      (a, b) => severityOrder[a.severity] - severityOrder[b.severity]
    );

    return vulnerabilities;
  }

  /**
   * Calculate overall risk level
   */
  private calculateRiskLevel(
    testResults: TestResult[]
  ): "low" | "medium" | "high" | "critical" {
    const vulnerabilities = this.extractVulnerabilities(testResults);

    const criticalCount = vulnerabilities.filter(
      (v) => v.severity === "critical"
    ).length;
    const highCount = vulnerabilities.filter(
      (v) => v.severity === "high"
    ).length;
    const mediumCount = vulnerabilities.filter(
      (v) => v.severity === "medium"
    ).length;

    if (criticalCount > 0) return "critical";
    if (highCount > 2) return "critical";
    if (highCount > 0 || mediumCount > 5) return "high";
    if (mediumCount > 0) return "medium";

    return "low";
  }

  /**
   * Generate security recommendations
   */
  private generateRecommendations(
    vulnerabilities: Vulnerability[],
    config: RedTeamConfig
  ): string[] {
    const recommendations: string[] = [];
    const vulnerabilityTypes = new Set(vulnerabilities.map((v) => v.type));

    // General recommendations based on vulnerability types found
    if (vulnerabilityTypes.has(VulnerabilityType.PROMPT_INJECTION)) {
      recommendations.push(
        "Implement robust input sanitization and validation to prevent prompt injection attacks",
        "Use system instructions vs. user instructions separation",
        "Consider implementing a prompt firewall or content filter"
      );
    }

    if (vulnerabilityTypes.has(VulnerabilityType.DATA_EXFILTRATION)) {
      recommendations.push(
        "Implement strict data access controls and output filtering",
        "Use differential privacy techniques for sensitive data",
        "Add data loss prevention (DLP) mechanisms"
      );
    }

    if (vulnerabilityTypes.has(VulnerabilityType.PRIVILEGE_ESCALATION)) {
      recommendations.push(
        "Implement deterministic, non-LLM-based permission systems",
        "Apply principle of least privilege for all operations",
        "Use granular role-based access control (RBAC)"
      );
    }

    if (vulnerabilityTypes.has(VulnerabilityType.MEMORY_POISONING)) {
      recommendations.push(
        "Implement temporal memory structures with limited persistence",
        "Segregate system instructions from user input memory",
        "Add memory attribution tracking"
      );
    }

    if (vulnerabilityTypes.has(VulnerabilityType.PII_LEAK)) {
      recommendations.push(
        "Implement PII detection and redaction mechanisms",
        "Use anonymization techniques for sensitive data",
        "Add output scanning for personally identifiable information"
      );
    }

    if (vulnerabilityTypes.has(VulnerabilityType.CONTEXT_INJECTION)) {
      recommendations.push(
        "Validate and sanitize all context sources",
        "Separate retrieved documents from system messages",
        "Implement content validation for knowledge base updates"
      );
    }

    if (vulnerabilityTypes.has(VulnerabilityType.TOOL_MANIPULATION)) {
      recommendations.push(
        "Implement strict input validation for all tool APIs",
        "Use API request signing and verification",
        "Treat all LLM tool APIs as public interfaces"
      );
    }

    // PentX-specific recommendations
    if (vulnerabilityTypes.has(VulnerabilityType.PENTX_WEB_VULNERABILITY)) {
      recommendations.push(
        "Address web application vulnerabilities identified by PentX autonomous testing",
        "Implement secure coding practices for web applications",
        "Regular security testing with automated tools",
        "Update and patch web application frameworks and dependencies"
      );
    }

    if (vulnerabilityTypes.has(VulnerabilityType.PENTX_API_VULNERABILITY)) {
      recommendations.push(
        "Secure API endpoints with proper authentication and authorization",
        "Implement API rate limiting and input validation",
        "Use API security gateways and monitoring",
        "Follow OWASP API Security Top 10 guidelines"
      );
    }

    if (
      vulnerabilityTypes.has(
        VulnerabilityType.PENTX_INFRASTRUCTURE_VULNERABILITY
      )
    ) {
      recommendations.push(
        "Harden infrastructure components and services",
        "Implement network segmentation and access controls",
        "Regular vulnerability scanning and patch management",
        "Monitor infrastructure for security anomalies"
      );
    }

    if (vulnerabilityTypes.has(VulnerabilityType.PENTX_AUTHENTICATION_BYPASS)) {
      recommendations.push(
        "Strengthen authentication mechanisms and multi-factor authentication",
        "Implement proper session management and timeout policies",
        "Regular authentication security audits",
        "Use secure authentication protocols and standards"
      );
    }

    if (vulnerabilityTypes.has(VulnerabilityType.PENTX_INJECTION_ATTACK)) {
      recommendations.push(
        "Implement comprehensive input validation and sanitization",
        "Use parameterized queries and prepared statements",
        "Apply principle of least privilege for database access",
        "Regular code review for injection vulnerabilities"
      );
    }

    if (
      vulnerabilityTypes.has(VulnerabilityType.PENTX_CRYPTOGRAPHIC_WEAKNESS)
    ) {
      recommendations.push(
        "Use strong cryptographic algorithms and key management",
        "Implement proper certificate management and validation",
        "Regular cryptographic security assessments",
        "Follow current cryptographic best practices and standards"
      );
    }

    // Target-specific recommendations
    if (config.target.type === "rag") {
      recommendations.push(
        "Regularly audit and validate RAG knowledge base content",
        "Implement retrieval result filtering and validation",
        "Use multiple retrieval strategies to reduce manipulation risk"
      );
    }

    if (config.target.type === "agent") {
      recommendations.push(
        "Limit agent action scope and implement action approval workflows",
        "Monitor agent behavior against established safety criteria",
        "Implement session limits for sensitive operations"
      );
    }

    // Add severity-based recommendations
    const criticalVulns = vulnerabilities.filter(
      (v) => v.severity === "critical"
    );
    if (criticalVulns.length > 0) {
      recommendations.unshift(
        "🚨 CRITICAL: Immediate action required - system should not be deployed to production",
        "Conduct thorough security review before any deployment"
      );
    }

    const highVulns = vulnerabilities.filter((v) => v.severity === "high");
    if (highVulns.length > 0) {
      recommendations.unshift(
        "⚠️ HIGH PRIORITY: Address high-severity vulnerabilities before production deployment"
      );
    }

    // Remove duplicates and return
    return [...new Set(recommendations)];
  }

  /**
   * Export report to different formats
   */
  async exportReport(
    report: RedTeamReport,
    format: "json" | "yaml" | "html" | "markdown"
  ): Promise<string> {
    switch (format) {
      case "json":
        return JSON.stringify(report, null, 2);

      case "yaml":
        return this.toYaml(report);

      case "html":
        return this.toHtml(report);

      case "markdown":
        return this.toMarkdown(report);

      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  }

  /**
   * Convert report to YAML format
   */
  private toYaml(report: RedTeamReport): string {
    // Simple YAML conversion - in production, use a proper YAML library
    const yaml = `
# Red Team Security Assessment Report

## Summary
total_tests: ${report.summary.totalTests}
passed_tests: ${report.summary.passedTests}
failed_tests: ${report.summary.failedTests}
vulnerabilities_found: ${report.summary.vulnerabilitiesFound}
overall_score: ${report.summary.overallScore}
risk_level: ${report.summary.riskLevel}

## Vulnerabilities
${report.vulnerabilities
  .map(
    (v) => `
- type: ${v.type}
  severity: ${v.severity}
  description: "${v.description}"
  evidence: "${v.evidence}"
  ${v.recommendation ? `recommendation: "${v.recommendation}"` : ""}
  ${v.cwe ? `cwe: "${v.cwe}"` : ""}
  ${v.owasp ? `owasp: "${v.owasp}"` : ""}
`
  )
  .join("")}

## Recommendations
${report.recommendations.map((r) => `- ${r}`).join("\n")}

## Metadata
timestamp: ${report.metadata.timestamp.toISOString()}
duration: ${report.metadata.duration}ms
version: ${report.metadata.version}
`.trim();

    return yaml;
  }

  /**
   * Convert report to HTML format
   */
  private toHtml(report: RedTeamReport): string {
    const riskColor = {
      low: "#28a745",
      medium: "#ffc107",
      high: "#fd7e14",
      critical: "#dc3545",
    };

    return `
<!DOCTYPE html>
<html>
<head>
    <title>Red Team Security Assessment Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 5px; }
        .summary { display: flex; gap: 20px; margin: 20px 0; }
        .metric { background: white; padding: 15px; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .risk-level { color: ${
          riskColor[report.summary.riskLevel]
        }; font-weight: bold; }
        .vulnerability { margin: 10px 0; padding: 15px; border-left: 4px solid #ddd; }
        .critical { border-left-color: #dc3545; }
        .high { border-left-color: #fd7e14; }
        .medium { border-left-color: #ffc107; }
        .low { border-left-color: #28a745; }
        .recommendations { background: #e7f3ff; padding: 20px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛡️ Red Team Security Assessment Report</h1>
        <p>Generated on ${report.metadata.timestamp.toLocaleString()}</p>
        <p>Assessment Duration: ${Math.round(
          report.metadata.duration / 1000
        )}s</p>
    </div>

    <div class="summary">
        <div class="metric">
            <h3>Total Tests</h3>
            <p style="font-size: 24px; margin: 0;">${
              report.summary.totalTests
            }</p>
        </div>
        <div class="metric">
            <h3>Vulnerabilities Found</h3>
            <p style="font-size: 24px; margin: 0;">${
              report.summary.vulnerabilitiesFound
            }</p>
        </div>
        <div class="metric">
            <h3>Overall Score</h3>
            <p style="font-size: 24px; margin: 0;">${
              report.summary.overallScore
            }/100</p>
        </div>
        <div class="metric">
            <h3>Risk Level</h3>
            <p style="font-size: 24px; margin: 0;" class="risk-level">${report.summary.riskLevel.toUpperCase()}</p>
        </div>
    </div>

    <h2>🚨 Vulnerabilities</h2>
    ${report.vulnerabilities
      .map(
        (v) => `
        <div class="vulnerability ${v.severity}">
            <h4>${v.type
              .replace(/_/g, " ")
              .toUpperCase()} - ${v.severity.toUpperCase()}</h4>
            <p><strong>Description:</strong> ${v.description}</p>
            <p><strong>Evidence:</strong> ${v.evidence}</p>
            ${
              v.recommendation
                ? `<p><strong>Recommendation:</strong> ${v.recommendation}</p>`
                : ""
            }
        </div>
    `
      )
      .join("")}

    <div class="recommendations">
        <h2>💡 Recommendations</h2>
        <ul>
            ${report.recommendations.map((r) => `<li>${r}</li>`).join("")}
        </ul>
    </div>
</body>
</html>
    `.trim();
  }

  /**
   * Convert report to Markdown format
   */
  private toMarkdown(report: RedTeamReport): string {
    const riskEmoji = {
      low: "🟢",
      medium: "🟡",
      high: "🟠",
      critical: "🔴",
    };

    return `
# 🛡️ Red Team Security Assessment Report

**Generated:** ${report.metadata.timestamp.toLocaleString()}  
**Duration:** ${Math.round(report.metadata.duration / 1000)}s  
**Version:** ${report.metadata.version}

## 📊 Summary

| Metric | Value |
|--------|-------|
| Total Tests | ${report.summary.totalTests} |
| Passed Tests | ${report.summary.passedTests} |
| Failed Tests | ${report.summary.failedTests} |
| Vulnerabilities Found | ${report.summary.vulnerabilitiesFound} |
| Overall Score | ${report.summary.overallScore}/100 |
| Risk Level | ${
      riskEmoji[report.summary.riskLevel]
    } ${report.summary.riskLevel.toUpperCase()} |

## 🚨 Vulnerabilities

${report.vulnerabilities
  .map(
    (v) => `
### ${v.type.replace(/_/g, " ").toUpperCase()} - ${v.severity.toUpperCase()}

**Description:** ${v.description}

**Evidence:** ${v.evidence}

${v.recommendation ? `**Recommendation:** ${v.recommendation}` : ""}

${v.cwe ? `**CWE:** ${v.cwe}` : ""}

${v.owasp ? `**OWASP:** ${v.owasp}` : ""}

---
`
  )
  .join("")}

## 💡 Recommendations

${report.recommendations.map((r) => `- ${r}`).join("\n")}

## 🔧 Configuration

**Target Type:** ${report.metadata.config.target.type}  
**Plugins Used:** ${report.metadata.config.plugins
      .map((p) => (typeof p === "string" ? p : p.id))
      .join(", ")}  
${
  report.metadata.config.strategies
    ? `**Strategies Used:** ${report.metadata.config.strategies
        .map((s) => (typeof s === "string" ? s : s.id))
        .join(", ")}`
    : ""
}
    `.trim();
  }

  /**
   * Generate PentX-specific report section
   */
  generatePentXSection(testResults: TestResult[]): string {
    const pentxResults = testResults.filter(
      (result) =>
        result.testCase.plugin === "pentx" && result.metadata?.pentxResults
    );

    if (pentxResults.length === 0) {
      return "";
    }

    let section = "\n## 🤖 PentX Autonomous Penetration Testing Results\n\n";

    for (const result of pentxResults) {
      const pentxData = result.metadata?.pentxResults as PentXResults;
      const metadata = result.testCase.metadata;

      section += `### ${metadata?.targetType?.toUpperCase()} Testing - ${
        metadata?.testPhase
      }\n\n`;
      section += `**Test Duration:** ${Math.round(
        pentxData.duration / 1000 / 60
      )} minutes\n`;
      section += `**Status:** ${pentxData.status}\n`;
      section += `**Vulnerabilities Found:** ${pentxData.vulnerabilities.length}\n\n`;

      if (pentxData.summary) {
        section += "**Summary:**\n";
        section += `- Pages Scanned: ${
          pentxData.summary.pagesScanned || "N/A"
        }\n`;
        section += `- Requests Made: ${
          pentxData.summary.requestsMade || "N/A"
        }\n`;
        section += `- Critical Issues: ${pentxData.summary.criticalIssues}\n`;
        section += `- High Risk Issues: ${pentxData.summary.highRiskIssues}\n`;
        section += `- Medium Risk Issues: ${pentxData.summary.mediumRiskIssues}\n`;
        section += `- Low Risk Issues: ${pentxData.summary.lowRiskIssues}\n\n`;
      }

      if (pentxData.vulnerabilities.length > 0) {
        section += "**Top Vulnerabilities:**\n";
        const topVulns = pentxData.vulnerabilities
          .slice(0, 5)
          .sort(
            (a, b) =>
              this.getSeverityWeight(b.severity) -
              this.getSeverityWeight(a.severity)
          );

        for (const vuln of topVulns) {
          const severityEmoji = this.getSeverityEmoji(vuln.severity);
          section += `- ${severityEmoji} **${
            vuln.title
          }** (${vuln.severity.toUpperCase()})\n`;
          section += `  - ${vuln.description}\n`;
          if (vuln.evidence.location) {
            section += `  - Location: ${vuln.evidence.location}\n`;
          }
        }
        section += "\n";
      }

      if (pentxData.recommendations.length > 0) {
        section += "**PentX Recommendations:**\n";
        const topRecommendations = pentxData.recommendations
          .filter(
            (rec) => rec.priority === "critical" || rec.priority === "high"
          )
          .slice(0, 3);

        for (const rec of topRecommendations) {
          section += `- **${rec.title}** (${rec.priority.toUpperCase()})\n`;
          section += `  - ${rec.description}\n`;
        }
        section += "\n";
      }
    }

    return section;
  }

  /**
   * Get severity weight for sorting
   */
  private getSeverityWeight(severity: string): number {
    const weights = { critical: 4, high: 3, medium: 2, low: 1, info: 0 };
    return weights[severity as keyof typeof weights] || 0;
  }

  /**
   * Get emoji for severity level
   */
  private getSeverityEmoji(severity: string): string {
    const emojis = {
      critical: "🔴",
      high: "🟠",
      medium: "🟡",
      low: "🟢",
      info: "🔵",
    };
    return emojis[severity as keyof typeof emojis] || "⚪";
  }

  /**
   * Enhanced markdown export with PentX section
   */
  async exportPentXReport(
    report: RedTeamReport,
    format: "markdown" | "html" = "markdown"
  ): Promise<string> {
    const baseReport = await this.exportReport(report, format);
    const pentxSection = this.generatePentXSection(report.testResults);

    if (format === "markdown") {
      // Insert PentX section before recommendations
      const recommendationsIndex = baseReport.indexOf("## 📋 Recommendations");
      if (recommendationsIndex !== -1 && pentxSection) {
        return (
          baseReport.slice(0, recommendationsIndex) +
          pentxSection +
          baseReport.slice(recommendationsIndex)
        );
      }
    }

    return baseReport + (pentxSection || "");
  }
}
