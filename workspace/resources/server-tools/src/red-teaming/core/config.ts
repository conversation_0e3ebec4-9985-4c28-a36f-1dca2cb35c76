import {
  RedTeamConfig,
  PluginConfig,
  StrategyConfig,
  ProviderConfig,
} from "../types";

/**
 * Configuration validator and manager for red teaming
 */
export class ConfigValidator {
  /**
   * Validate red team configuration
   */
  async validate(config: RedTeamConfig): Promise<void> {
    this.validateTarget(config.target);
    this.validatePlugins(config.plugins);

    if (config.strategies) {
      this.validateStrategies(config.strategies);
    }

    if (config.providers) {
      this.validateProviders(config.providers);
    }

    this.validateTestSettings(config);
  }

  /**
   * Validate target configuration
   */
  private validateTarget(target: RedTeamConfig["target"]): void {
    if (!target.type) {
      throw new Error("Target type is required");
    }

    const validTypes = ["rag", "agent", "llm", "hybrid"];
    if (!validTypes.includes(target.type)) {
      throw new Error(
        `Invalid target type: ${target.type}. Must be one of: ${validTypes.join(
          ", "
        )}`
      );
    }

    // Validate endpoint if provided
    if (target.endpoint) {
      try {
        new URL(target.endpoint);
      } catch (error) {
        throw new Error(`Invalid target endpoint URL: ${target.endpoint}`);
      }
    }
  }

  /**
   * Validate plugin configurations
   */
  private validatePlugins(plugins: Array<string | PluginConfig>): void {
    if (!plugins || plugins.length === 0) {
      throw new Error("At least one plugin must be specified");
    }

    for (const plugin of plugins) {
      if (typeof plugin === "string") {
        if (!plugin.trim()) {
          throw new Error("Plugin ID cannot be empty");
        }
      } else {
        if (!plugin.id || !plugin.id.trim()) {
          throw new Error("Plugin ID is required");
        }
      }
    }
  }

  /**
   * Validate strategy configurations
   */
  private validateStrategies(strategies: Array<string | StrategyConfig>): void {
    for (const strategy of strategies) {
      if (typeof strategy === "string") {
        if (!strategy.trim()) {
          throw new Error("Strategy ID cannot be empty");
        }
      } else {
        if (!strategy.id || !strategy.id.trim()) {
          throw new Error("Strategy ID is required");
        }
      }
    }
  }

  /**
   * Validate provider configurations
   */
  private validateProviders(providers: ProviderConfig[]): void {
    for (const provider of providers) {
      if (!provider.id || !provider.id.trim()) {
        throw new Error("Provider ID is required");
      }

      if (!provider.type || !provider.type.trim()) {
        throw new Error("Provider type is required");
      }

      const validTypes = ["python", "javascript", "http", "custom"];
      if (!validTypes.includes(provider.type)) {
        throw new Error(
          `Invalid provider type: ${
            provider.type
          }. Must be one of: ${validTypes.join(", ")}`
        );
      }
    }
  }

  /**
   * Validate test settings
   */
  private validateTestSettings(config: RedTeamConfig): void {
    if (config.numTests !== undefined) {
      if (!Number.isInteger(config.numTests) || config.numTests <= 0) {
        throw new Error("numTests must be a positive integer");
      }
    }

    if (config.maxConcurrency !== undefined) {
      if (
        !Number.isInteger(config.maxConcurrency) ||
        config.maxConcurrency <= 0
      ) {
        throw new Error("maxConcurrency must be a positive integer");
      }
    }
  }
}

/**
 * Configuration builder for creating red team configurations
 */
export class RedTeamConfigBuilder {
  private config: Partial<RedTeamConfig> = {};

  /**
   * Set target configuration
   */
  target(
    type: "rag" | "agent" | "llm" | "hybrid",
    options?: {
      endpoint?: string;
      provider?: string;
      model?: string;
    }
  ): RedTeamConfigBuilder {
    this.config.target = {
      type,
      ...options,
    };
    return this;
  }

  /**
   * Set purpose description
   */
  purpose(purpose: string): RedTeamConfigBuilder {
    this.config.purpose = purpose;
    return this;
  }

  /**
   * Set number of tests
   */
  numTests(count: number): RedTeamConfigBuilder {
    this.config.numTests = count;
    return this;
  }

  /**
   * Set maximum concurrency
   */
  maxConcurrency(count: number): RedTeamConfigBuilder {
    this.config.maxConcurrency = count;
    return this;
  }

  /**
   * Add plugin
   */
  plugin(id: string, config?: Record<string, any>): RedTeamConfigBuilder {
    if (!this.config.plugins) {
      this.config.plugins = [];
    }

    if (config) {
      this.config.plugins.push({ id, config });
    } else {
      this.config.plugins.push(id);
    }

    return this;
  }

  /**
   * Add multiple plugins
   */
  plugins(plugins: Array<string | PluginConfig>): RedTeamConfigBuilder {
    this.config.plugins = [...(this.config.plugins || []), ...plugins];
    return this;
  }

  /**
   * Add strategy
   */
  strategy(id: string, config?: Record<string, any>): RedTeamConfigBuilder {
    if (!this.config.strategies) {
      this.config.strategies = [];
    }

    if (config) {
      this.config.strategies.push({ id, config });
    } else {
      this.config.strategies.push(id);
    }

    return this;
  }

  /**
   * Add provider
   */
  provider(
    id: string,
    type: "python" | "javascript" | "http" | "custom",
    config: Record<string, any>
  ): RedTeamConfigBuilder {
    if (!this.config.providers) {
      this.config.providers = [];
    }

    this.config.providers.push({ id, type, config });
    return this;
  }

  /**
   * Set output configuration
   */
  output(options: {
    format?: "json" | "yaml" | "html" | "markdown";
    file?: string;
    includeDetails?: boolean;
  }): RedTeamConfigBuilder {
    this.config.output = options;
    return this;
  }

  /**
   * Build the configuration
   */
  build(): RedTeamConfig {
    if (!this.config.target) {
      throw new Error("Target configuration is required");
    }

    if (!this.config.plugins || this.config.plugins.length === 0) {
      throw new Error("At least one plugin is required");
    }

    return this.config as RedTeamConfig;
  }
}

/**
 * Predefined configuration templates
 */
export class ConfigTemplates {
  /**
   * RAG security assessment template
   */
  static ragSecurity(
    options: {
      endpoint?: string;
      purpose?: string;
      numTests?: number;
    } = {}
  ): RedTeamConfig {
    return new RedTeamConfigBuilder()
      .target("rag", { endpoint: options.endpoint })
      .purpose(options.purpose || "RAG system security assessment")
      .numTests(options.numTests || 50)
      .plugins([
        "prompt-injection",
        "context-injection",
        "data-exfiltration",
        "pii-leak",
        "harmful-content",
        {
          id: "policy",
          config: {
            policy:
              "The system must not disclose sensitive information or generate harmful content",
          },
        },
      ])
      .strategy("jailbreak")
      .strategy("prompt-injection")
      .build();
  }

  /**
   * Agent security assessment template
   */
  static agentSecurity(
    options: {
      endpoint?: string;
      purpose?: string;
      numTests?: number;
    } = {}
  ): RedTeamConfig {
    return new RedTeamConfigBuilder()
      .target("agent", { endpoint: options.endpoint })
      .purpose(options.purpose || "AI agent security assessment")
      .numTests(options.numTests || 40)
      .plugins([
        "rbac",
        "privilege-escalation",
        "tool-manipulation",
        "memory-poisoning",
        "excessive-agency",
        "ssrf",
        "sql-injection",
      ])
      .strategy("multi-stage-attack")
      .strategy("jailbreak")
      .build();
  }

  /**
   * Comprehensive security assessment template
   */
  static comprehensive(
    options: {
      endpoint?: string;
      purpose?: string;
      numTests?: number;
    } = {}
  ): RedTeamConfig {
    return new RedTeamConfigBuilder()
      .target("hybrid", { endpoint: options.endpoint })
      .purpose(options.purpose || "Comprehensive AI system security assessment")
      .numTests(options.numTests || 100)
      .plugins([
        "prompt-injection",
        "context-injection",
        "data-exfiltration",
        "pii-leak",
        "harmful-content",
        "rbac",
        "privilege-escalation",
        "tool-manipulation",
        "memory-poisoning",
        "excessive-agency",
        "ssrf",
        "sql-injection",
        "hallucination",
        "jailbreak",
      ])
      .strategy("jailbreak")
      .strategy("prompt-injection")
      .strategy("multi-stage-attack")
      .strategy("crescendo")
      .build();
  }

  /**
   * PentX web application penetration testing template
   */
  static pentxWebApp(
    options: {
      url?: string;
      purpose?: string;
      depth?: "shallow" | "medium" | "deep";
      timeLimit?: number;
      aggressive?: boolean;
    } = {}
  ): RedTeamConfig {
    return new RedTeamConfigBuilder()
      .target("hybrid", { endpoint: options.url })
      .purpose(
        options.purpose ||
          "PentX autonomous web application penetration testing"
      )
      .numTests(1) // PentX handles multiple tests internally
      .plugins([
        {
          id: "pentx",
          config: {
            targetType: "web-app",
            url: options.url,
            scope: {
              depth: options.depth || "medium",
              timeLimit: options.timeLimit || 60,
            },
            options: {
              aggressive: options.aggressive || false,
              stealthMode: true,
              socialEngineering: false,
              physicalTesting: false,
              complianceChecks: true,
            },
          },
        },
      ])
      .build();
  }

  /**
   * PentX API security assessment template
   */
  static pentxAPI(
    options: {
      baseUrl?: string;
      endpoints?: string[];
      purpose?: string;
      depth?: "shallow" | "medium" | "deep";
      timeLimit?: number;
      credentials?: any;
    } = {}
  ): RedTeamConfig {
    return new RedTeamConfigBuilder()
      .target("hybrid", { endpoint: options.baseUrl })
      .purpose(options.purpose || "PentX autonomous API security assessment")
      .numTests(1)
      .plugins([
        {
          id: "pentx",
          config: {
            targetType: "api",
            url: options.baseUrl,
            endpoints: options.endpoints,
            credentials: options.credentials,
            scope: {
              depth: options.depth || "medium",
              timeLimit: options.timeLimit || 45,
            },
            options: {
              aggressive: false,
              stealthMode: true,
              socialEngineering: false,
              physicalTesting: false,
              complianceChecks: true,
            },
          },
        },
      ])
      .build();
  }

  /**
   * PentX infrastructure penetration testing template
   */
  static pentxInfrastructure(
    options: {
      ipRanges?: string[];
      purpose?: string;
      depth?: "shallow" | "medium" | "deep";
      timeLimit?: number;
      aggressive?: boolean;
    } = {}
  ): RedTeamConfig {
    return new RedTeamConfigBuilder()
      .target("hybrid")
      .purpose(
        options.purpose || "PentX autonomous infrastructure penetration testing"
      )
      .numTests(1)
      .plugins([
        {
          id: "pentx",
          config: {
            targetType: "infrastructure",
            ipRanges: options.ipRanges || ["***********/24"],
            scope: {
              depth: options.depth || "medium",
              timeLimit: options.timeLimit || 90,
            },
            options: {
              aggressive: options.aggressive || false,
              stealthMode: true,
              socialEngineering: false,
              physicalTesting: false,
              complianceChecks: true,
            },
          },
        },
      ])
      .build();
  }

  /**
   * PentX comprehensive autonomous penetration testing template
   */
  static pentxComprehensive(
    options: {
      url?: string;
      ipRanges?: string[];
      purpose?: string;
      timeLimit?: number;
      aggressive?: boolean;
    } = {}
  ): RedTeamConfig {
    return new RedTeamConfigBuilder()
      .target("hybrid", { endpoint: options.url })
      .purpose(
        options.purpose || "PentX comprehensive autonomous penetration testing"
      )
      .numTests(3) // Web, API, and Infrastructure tests
      .plugins([
        {
          id: "pentx",
          config: {
            targetType: "web-app",
            url: options.url,
            scope: {
              depth: "deep",
              timeLimit: options.timeLimit || 120,
            },
            options: {
              aggressive: options.aggressive || false,
              stealthMode: false,
              socialEngineering: true,
              physicalTesting: false,
              complianceChecks: true,
            },
          },
        },
        {
          id: "pentx",
          config: {
            targetType: "api",
            url: options.url,
            scope: {
              depth: "deep",
              timeLimit: options.timeLimit || 90,
            },
            options: {
              aggressive: options.aggressive || false,
              stealthMode: false,
              socialEngineering: false,
              physicalTesting: false,
              complianceChecks: true,
            },
          },
        },
        {
          id: "pentx",
          config: {
            targetType: "infrastructure",
            ipRanges: options.ipRanges,
            scope: {
              depth: "deep",
              timeLimit: options.timeLimit || 150,
            },
            options: {
              aggressive: options.aggressive || false,
              stealthMode: false,
              socialEngineering: false,
              physicalTesting: false,
              complianceChecks: true,
            },
          },
        },
      ])
      .build();
  }
}
