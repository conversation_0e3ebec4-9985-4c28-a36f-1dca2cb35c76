
import { ToolType } from "./type";

import { NANO_MULTIPLIER } from "../../../../../money";
import { calculateTokens } from "../../common/pricing";

// https://developers.cloudflare.com/workers-ai/models/llama-3.3-70b-instruct-fp8-fast/
// $0.29 / 1,000,000
// When we have a million tokens, we get charged $2.50 for input
const INPUT_COST_PER_TOKEN = NANO_MULTIPLIER * 29n / 100n / 1_000_000n;
// $2.25 / 1,000,000
const OUTPUT_COST_PER_TOKEN = NANO_MULTIPLIER * 2_25n / 100n / 1_000_000n;

// https://platform.openai.com/docs/models/gpt-4o-mini
const MAX_OUTPUT_TOKENS = BigInt(Math.pow(2, 14));

// Apparently, the 3.1 and 3.3 are functionally the same counters
// https://huggingface.co/meta-llama/Llama-3.3-70B-Instruct/discussions/16?utm_source=chatgpt.com
import { COUNT_LLAMA3_TOKENS } from "../llama-3_1/pricing";
export const PRICING: ToolType["pricing"] = {
  async getEstimatedCost(config, input){
    const countTokens = await COUNT_LLAMA3_TOKENS;
    return {
      inputCost: INPUT_COST_PER_TOKEN * calculateTokens(
        countTokens, input.thread
      ),
      outputEscrow: MAX_OUTPUT_TOKENS * OUTPUT_COST_PER_TOKEN,
      costStatus: "maximum",
    };
  },

  async getFinalCost(config, input, output){
    const countTokens = await COUNT_LLAMA3_TOKENS;
    return {
      outputCost: BigInt(countTokens(output.text)) * OUTPUT_COST_PER_TOKEN
    };
  },
};

