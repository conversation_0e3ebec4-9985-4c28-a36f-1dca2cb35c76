
import { ToolType } from "./type";

import { NANO_MULTIPLIER } from "../../../../../money";
import { countTokens } from "gpt-tokenizer/model/gpt-4o-mini";
import { calculateTokens } from "../../common/pricing";

// https://platform.openai.com/docs/pricing#latest-models
// $0.15 / 1,000,000
// When we have a million tokens, we get charged $0.15 for input
export const INPUT_COST_PER_TOKEN = NANO_MULTIPLIER * 15n / 100n / 1_000_000n;
// $0.60 / 1,000,000
export const OUTPUT_COST_PER_TOKEN = NANO_MULTIPLIER * 60n / 100n / 1_000_000n;
export { countTokens };

// https://platform.openai.com/docs/models/gpt-4o-mini
const MAX_OUTPUT_TOKENS = 16_384n;

export const PRICING: ToolType["pricing"] = {
  async getEstimatedCost(config, input){
    return {
      inputCost: INPUT_COST_PER_TOKEN * calculateTokens(
        countTokens, input.thread
      ),
      outputEscrow: MAX_OUTPUT_TOKENS * OUTPUT_COST_PER_TOKEN,
      costStatus: "maximum",
    };
  },

  async getFinalCost(config, input, output){
    return {
      outputCost: BigInt(countTokens(output.text)) * OUTPUT_COST_PER_TOKEN
    };
  },
};

