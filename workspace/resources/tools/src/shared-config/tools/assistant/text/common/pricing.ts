
export function calculateTokens(
  countTokens: (text: string)=>number, thread: Array<{ content: string }>
){
  let totalTokens = 0;
  for(const message of thread){
    totalTokens += countTokens(message.content);
  }
  return BigInt(totalTokens);
}

export async function calculateTokensAsync(
  countTokens: (text: string)=>Promise<number>,
  thread: Array<{ content: string }>
){
  let totalTokens = 0n;
  await Promise.all(thread.map(async (message)=>{
    totalTokens += BigInt(await countTokens(message.content));
  }));
  return totalTokens;
}
