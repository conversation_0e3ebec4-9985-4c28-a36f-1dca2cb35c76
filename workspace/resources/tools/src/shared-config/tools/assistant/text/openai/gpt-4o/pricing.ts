
import { ToolType } from "./type";

import { NANO_MULTIPLIER } from "../../../../../money";
import { countTokens } from "gpt-tokenizer/model/gpt-4o";
import { calculateTokens } from "../../common/pricing";

// https://platform.openai.com/docs/pricing#latest-models
// $2.50 / 1,000,000
// When we have a million tokens, we get charged $2.50 for input
const INPUT_COST_PER_TOKEN = NANO_MULTIPLIER * 2_50n / 100n / 1_000_000n;
// $10.00 / 1,000,000
const OUTPUT_COST_PER_TOKEN = NANO_MULTIPLIER * 10_00n / 100n / 1_000_000n;

// https://platform.openai.com/docs/models/gpt-4o-mini
const MAX_OUTPUT_TOKENS = 16_384n;

export const PRICING: ToolType["pricing"] = {
  async getEstimatedCost(config, input){
    return {
      inputCost: INPUT_COST_PER_TOKEN * calculateTokens(
        countTokens, input.thread
      ),
      outputEscrow: MAX_OUTPUT_TOKENS * OUTPUT_COST_PER_TOKEN,
      costStatus: "maximum",
    };
  },

  async getFinalCost(config, input, output){
    return {
      outputCost: BigInt(countTokens(output.text)) * OUTPUT_COST_PER_TOKEN
    };
  },
};

