
import { ToolType } from "./type";

export const CONFIG: ToolType["inputConfig"] = {
  schema: {
    maxTokens: "number?",
  },
  default: {
    maxTokens: 256,
  },
  validate: (config)=>{
    if(config.maxTokens && config.maxTokens < 1){
      return [{
        key: "maxTokens",
        error: "Maximum Tokens should be greater than 0",
        meta: "maxTokens"
      }];
    }
    return [];
  }
};
