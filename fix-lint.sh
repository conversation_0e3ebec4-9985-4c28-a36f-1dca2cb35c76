#!/bin/bash

# Fix arrow function spacing
find . -name "*.js" -o -name "*.ts" | xargs sed -i 's/ => /=>/g'

# Fix string quotes
find . -name "*.js" -o -name "*.ts" | xargs sed -i "s/'/\"/g"

# Fix if/while spacing
find . -name "*.js" -o -name "*.ts" | xargs sed -i 's/if (/if(/g'
find . -name "*.js" -o -name "*.ts" | xargs sed -i 's/while (/while(/g'

# Fix async spacing
find . -name "*.js" -o -name "*.ts" | xargs sed -i 's/async ()/async()/g'
